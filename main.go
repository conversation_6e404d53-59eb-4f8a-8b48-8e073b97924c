package main

import (
	"context"
	"embed"
	"errors"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/services"
	"henghengman-desktop/backend/storage"
	"henghengman-desktop/backend/types"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

func newAppOption(
	pref *storage.PreferencesStorage,
	prefSvc *services.PreferencesService,
	csSvc *services.ChecksumService,
	extractSvc *services.ExtractService,
	dialogSvc *services.DialogService,
	converter *services.ConverterService,
	downloader *services.Downloader,
	appSvc *services.AppService,
	updateSvc *services.UpdateService,
) *options.App {
	return &options.App{
		Title:  pref.GetPreferences().App.Name,
		Width:  constant.DefaultWindowWidth,
		Height: constant.DefaultWindowHeight,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		DragAndDrop: &options.DragAndDrop{
			EnableFileDrop:     true,
			DisableWebViewDrop: false,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup: func(ctx context.Context) {
			appSvc.Start(ctx)
			prefSvc.Start(ctx)
			csSvc.Start(ctx)
			extractSvc.Start(ctx)
			dialogSvc.Start(ctx)
			converter.Start(ctx)
			downloader.Start(ctx)
			updateSvc.Start(ctx)
		},
		OnDomReady: func(ctx context.Context) {
			appSvc.OnDomReady()
		},
		OnShutdown: func(ctx context.Context) {
			appSvc.OnShutdown()
		},
		OnBeforeClose: func(ctx context.Context) bool {
			appSvc.OnBeforeClose()
			return false
		},
		ErrorFormatter: func(err error) any {
			var jsErr *types.JSError
			if errors.As(err, &jsErr) {
				return jsErr
			}
			return err
		},
		SingleInstanceLock: &options.SingleInstanceLock{
			UniqueId: constant.DefaultUniqueId,
		},
		Bind: []interface{}{
			appSvc,
			prefSvc,
			csSvc,
			extractSvc,
			dialogSvc,
			converter,
			downloader,
			updateSvc,
		},
		EnumBind: []interface{}{
			types.AllNetworkRules,
			types.AllConverterTypes,
			types.AllDownloadOutputTypes,
		},
	}
}

func main() {
	if err := wails.Run(InitAppOption()); err != nil {
		panic(err)
	}
}
