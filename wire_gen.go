// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/wailsapp/wails/v2/pkg/options"
	"henghengman-desktop/backend/pkg/task"
	"henghengman-desktop/backend/services"
	"henghengman-desktop/backend/storage"
)

// Injectors from wire.go:

func InitAppOption() *options.App {
	preferencesStorage := storage.NewPreferences()
	preferencesService := services.NewPreferencesService(preferencesStorage)
	manager := task.NewManager()
	checksumService := services.NewChecksumService(manager)
	extractService := services.NewExtractService(preferencesStorage, manager)
	dialogService := services.NewDialogService()
	converterService := services.NewConverterService(manager)
	downloader := services.NewDownloader(preferencesStorage, manager)
	appService := services.NewAppService(preferencesStorage)
	updateService := services.NewUpdateService(preferencesStorage)
	app := newAppOption(preferencesStorage, preferencesService, checksumService, extractService, dialogService, converterService, downloader, appService, updateService)
	return app
}
