TOOLS_DIR := "tools"

fi:
	@pnpm install --prefix ./frontend

gm:
	@wails generate module

copy-windows-libs:
	@mkdir -p build/windows/installer
	@cp "$(TOOLS_DIR)/ffmpeg.exe" build/windows/installer/ffmpeg.exe
	@cp "$(TOOLS_DIR)/ffprobe.exe" build/windows/installer/ffprobe.exe
	@cp "$(TOOLS_DIR)/yt-dlp.exe" build/windows/installer/yt-dlp.exe
	@echo "Windows 所需文件复制完成"

build-windows: copy-windows-libs
	@echo "正在构建 Windows 安装包..."
	@wails build -platform windows/amd64 --nsis -skipbindings
	@echo "构建完成"
	@rm -f build/windows/installer/ffmpeg.exe
	@rm -f build/windows/installer/ffprobe.exe
	@rm -f build/windows/installer/yt-dlp.exe
	@echo "已清理 Windows 所需文件"

copy-mac-libs:
	@mkdir -p build/bin/henghengman-desktop.app/Contents/MacOS/$(TOOLS_DIR)
	@cp "$(TOOLS_DIR)/ffmpeg" build/bin/henghengman-desktop.app/Contents/MacOS/$(TOOLS_DIR)/ffmpeg
	@cp "$(TOOLS_DIR)/ffprobe" build/bin/henghengman-desktop.app/Contents/MacOS/$(TOOLS_DIR)/ffprobe
	@cp "$(TOOLS_DIR)/yt-dlp" build/bin/henghengman-desktop.app/Contents/MacOS/$(TOOLS_DIR)/yt-dlp
	@cp -r "$(TOOLS_DIR)/_internal" build/bin/henghengman-desktop.app/Contents/MacOS/$(TOOLS_DIR)/_internal
	@echo "Mac 所需文件复制完成"

build-mac:
	@echo "正在构建 Mac 安装包..."
	@wails build -platform darwin/arm64
	@echo "构建完成"

# 生成 wire 文件
wire:
	wire .