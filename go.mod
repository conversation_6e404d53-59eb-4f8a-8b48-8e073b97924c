module henghengman-desktop

go 1.24.4

require (
	dario.cat/mergo v1.0.2
	github.com/apparentlymart/go-userdirs v0.0.0-20200915174352-b0c018a67c13
	github.com/google/uuid v1.6.0
	github.com/google/wire v0.6.0
	github.com/jeandeaual/go-locale v0.0.0-20250612000132-0ef82f21eade
	github.com/mattn/go-ieproxy v0.0.12
	github.com/spf13/cast v1.9.2
	github.com/thlib/go-timezone-local v0.0.7
	github.com/wailsapp/wails/v2 v2.10.2
	golang.org/x/sync v0.16.0
	golang.org/x/text v0.27.0
	gopkg.in/yaml.v3 v3.0.1
)

require (
	github.com/bep/debounce v1.2.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.9 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/godbus/dbus/v5 v5.1.0 // indirect
	github.com/gorilla/websocket v1.5.3 // indirect
	github.com/jchv/go-winloader v0.0.0-20250406163304-c1995be93bd1 // indirect
	github.com/labstack/echo/v4 v4.13.4 // indirect
	github.com/labstack/gommon v0.4.2 // indirect
	github.com/leaanthony/go-ansi-parser v1.6.1 // indirect
	github.com/leaanthony/gosod v1.0.4 // indirect
	github.com/leaanthony/slicer v1.6.0 // indirect
	github.com/leaanthony/u v1.1.1 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/pkg/browser v0.0.0-20240102092130-5ac0b6a4141c // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/samber/lo v1.51.0 // indirect
	github.com/tkrajina/go-reflector v0.5.8 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	github.com/wailsapp/go-webview2 v1.0.21 // indirect
	github.com/wailsapp/mimetype v1.4.1 // indirect
	golang.org/x/crypto v0.40.0 // indirect
	golang.org/x/net v0.42.0 // indirect
	golang.org/x/sys v0.34.0 // indirect
)

// replace github.com/wailsapp/wails/v2 v2.10.1 => /Users/<USER>/go/pkg/mod
