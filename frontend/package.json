{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@tanstack/react-form": "^1.15.0", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.130.2", "clsx": "^2.1.1", "flowbite": "^3.1.2", "flowbite-react": "^0.12.5", "flowbite-react-icons": "^1.3.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "immer": "^10.1.1", "mitt": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.6.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "ts-pattern": "^5.8.0", "zod": "^4.0.11", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@tanstack/router-plugin": "^1.130.2", "@types/node": "^24.1.0", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@vitejs/plugin-react": "^4.7.0", "typescript": "^5.8.3", "vite": "^7.0.6"}}