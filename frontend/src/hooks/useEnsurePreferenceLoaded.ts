import { useEffect, useRef } from "react";
import { useLoadPreferences } from "@/store/preference";
import { types } from "@/../wailsjs/go/models";
import { PREFERENCE_LOAD_RETRY } from "@/constant/preference-load-retry";
export const useEnsurePreferenceLoaded = (preference: types.PreferencesGeneral | types.PreferencesUser | types.PreferencesNetwork | types.PreferencesApp | types.PreferencesServer | null) => {
  const { loadPreferences } = useLoadPreferences();
  const loadedRetry = useRef(PREFERENCE_LOAD_RETRY);
  useEffect(() => { 
    if (!preference && loadedRetry.current > 0) {
    loadPreferences();
    loadedRetry.current--;
  }
},[preference]);
}