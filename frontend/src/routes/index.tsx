import { createFileRoute } from "@tanstack/react-router";
import { Button, TextInput } from "flowbite-react";
import { useTranslation } from "react-i18next";
import { SingleDownloadUpperSection } from "../components/upper-section";
import { SingleDownloadBottomSection } from "../components/botton-section";
import { eventBus } from "../lib/event-bus";

export const Route = createFileRoute("/")({
  component: SingleDownload,
});

function SingleDownload() {
  const { t, i18n } = useTranslation();
  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
  };
  return (
    <div className="flex flex-col h-full p-6 gap-6">
      <SingleDownloadUpperSection title={t("front.video")} />
      <SingleDownloadBottomSection />
    </div>
  );
}
