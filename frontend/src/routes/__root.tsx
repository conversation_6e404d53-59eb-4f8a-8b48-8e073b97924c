import * as React from "react";
import { Outlet, createRootRoute } from "@tanstack/react-router";
import LeftSidebar from "../components/left-sidebar";
import { CustomTheme } from "../components/custom-theme";
import { SettingsModal } from "../components/settings-modal/settings-modal";
import { ThemeInit } from "@/../.flowbite-react/init";
import { LanguageGuard } from "../guard/language-guard";
export const Route = createRootRoute({
  component: RootComponent,
});

function RootComponent() {
  return (
    <React.Fragment>
      <ThemeInit />
      <CustomTheme>
        <div className="flex h-screen w-screen overflow-hidden bg-white">
          <LeftSidebar />
          <main className="flex-1 overflow-auto">
            <Outlet />
          </main>
        </div>
      </CustomTheme>
      <SettingsModal />
      <LanguageGuard />
    </React.Fragment>
  );
}
