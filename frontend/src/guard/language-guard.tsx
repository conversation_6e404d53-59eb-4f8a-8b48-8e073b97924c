import { useEffect } from "react";
import { useGeneralPreferences } from "../store/preference";
import { useTranslation } from "react-i18next";
export function LanguageGuard() {
    const { general } = useGeneralPreferences();
    const { i18n } = useTranslation();
    useEffect(() => {
        if (!general) {
            return;
        }
        const language = general.language;
        if (language) {
            i18n.changeLanguage(language);
        }
    }, [general]);
  return null;
}