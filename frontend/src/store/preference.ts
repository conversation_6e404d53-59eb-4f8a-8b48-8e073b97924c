import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
// import { devtools } from 'zustand/middleware/devtools'
import { types } from '../../wailsjs/go/models'
import * as PreferencesService from '../../wailsjs/go/services/PreferencesService'
import { createWithEqualityFn } from 'zustand/traditional'
import { shallow } from 'zustand/shallow'

// Granular state interface for better control
interface PreferencesStore {
  // Individual preference sections
  user: types.PreferencesUser | null
  general: types.PreferencesGeneral | null
  network: types.PreferencesNetwork | null
  app: types.PreferencesApp | null
  server: types.PreferencesServer | null
  
  // Loading states for each section
  loading: {
    user: boolean
    general: boolean
    network: boolean
    app: boolean
    server: boolean
    global: boolean
    restoring: boolean
  }
  
  // Error handling
  error: string | null
  
  // Dirty state tracking
  isDirty: {
    user: boolean
    general: boolean
    network: boolean
    app: boolean
    server: boolean
  }
  
  // Actions
  loadPreferences: () => Promise<void>
  
  // Granular update methods
  updateUser: (user: Partial<types.PreferencesUser>) => void
  updateGeneral: (general: Partial<types.PreferencesGeneral>) => void
  updateNetwork: (network: Partial<types.PreferencesNetwork>) => void
  updateApp: (app: Partial<types.PreferencesApp>) => void
  updateServer: (server: Partial<types.PreferencesServer>) => void
  
  // Save methods
  saveUser: () => Promise<void>
  saveGeneral: () => Promise<void>
  saveNetwork: () => Promise<void>
  saveApp: () => Promise<void>
  saveServer: () => Promise<void>
  saveAll: () => Promise<void>
  
  // Utility methods
  restoreDefaults: () => Promise<void>
  clearError: () => void
  resetDirtyState: () => void
  
  // Batch operations
  updateMultiple: (updates: {
    user?: Partial<types.PreferencesUser>
    general?: Partial<types.PreferencesGeneral>
    network?: Partial<types.PreferencesNetwork>
    app?: Partial<types.PreferencesApp>
    server?: Partial<types.PreferencesServer>
  }) => void
  
  // Internal helper (not meant for external use)
  _getFullPreferences: () => types.Preferences
}

export const usePreferencesStore = createWithEqualityFn<PreferencesStore>()(
    immer((set, get) => ({
      // Initial state
      user: null,
      general: null,
      network: null,
      app: null,
      server: null,
      
      loading: {
        user: false,
        general: false,
        network: false,
        app: false,
        server: false,
        global: false,
        restoring: false,
      },
      
      error: null,
      
      isDirty: {
        user: false,
        general: false,
        network: false,
        app: false,
        server: false,
      },
      
      // Load all preferences from backend
      loadPreferences: async () => {
        set((state) => {
          state.loading.global = true
          state.error = null
        })
        
        try {
          const preferences = await PreferencesService.GetPreferences()
          
          set((state) => {
            state.user = preferences.user
            state.general = preferences.general
            state.network = preferences.network
            state.app = preferences.app
            state.server = preferences.server
            state.loading.global = false
            state.isDirty = {
              user: false,
              general: false,
              network: false,
              app: false,
              server: false,
            }
          })
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load preferences'
            state.loading.global = false
          })
        }
      },
      
      // Granular update methods
      updateUser: (userUpdates) => {
        set((state) => {
          if (state.user) {
            Object.assign(state.user, userUpdates)
            state.isDirty.user = true
          }
        })
      },
      
      updateGeneral: (generalUpdates) => {
        set((state) => {
          if (state.general) {
            Object.assign(state.general, generalUpdates)
            state.isDirty.general = true
          }
        })
      },
      
      updateNetwork: (networkUpdates) => {
        set((state) => {
          if (state.network) {
            Object.assign(state.network, networkUpdates)
            state.isDirty.network = true
          }
        })
      },
      
      updateApp: (appUpdates) => {
        set((state) => {
          if (state.app) {
            Object.assign(state.app, appUpdates)
            state.isDirty.app = true
          }
        })
      },
      
      updateServer: (serverUpdates) => {
        set((state) => {
          if (state.server) {
            Object.assign(state.server, serverUpdates)
            state.isDirty.server = true
          }
        })
      },
      
      // Helper method to create full preferences object
      _getFullPreferences: () => {
        const state = get()
        if (!state.user || !state.general || !state.network || !state.app || !state.server) {
          throw new Error('Preferences not fully loaded')
        }
        
        return {
          user: state.user,
          general: state.general,
          network: state.network,
          app: state.app,
          server: state.server,
        } as types.Preferences
      },
      
      // Save methods for individual sections
      saveUser: async () => {
        const state = get()
        if (!state.isDirty.user) return
        
        set((draft) => { draft.loading.user = true })
        
        try {
          const fullPreferences = state._getFullPreferences()
          await PreferencesService.SetPreferences(fullPreferences)
          
          set((draft) => {
            draft.loading.user = false
            draft.isDirty.user = false
            draft.error = null
          })
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to save user preferences'
            draft.loading.user = false
          })
        }
      },
      
      saveGeneral: async () => {
        const state = get()
        if (!state.isDirty.general) return
        
        set((draft) => { draft.loading.general = true })
        
        try {
          const fullPreferences = state._getFullPreferences()
          await PreferencesService.SetPreferences(fullPreferences)
          
          set((draft) => {
            draft.loading.general = false
            draft.isDirty.general = false
            draft.error = null
          })
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to save general preferences'
            draft.loading.general = false
          })
        }
      },
      
      saveNetwork: async () => {
        const state = get()
        if (!state.isDirty.network) return
        
        set((draft) => { draft.loading.network = true })
        
        try {
          const fullPreferences = state._getFullPreferences()
          await PreferencesService.SetPreferences(fullPreferences)
          
          set((draft) => {
            draft.loading.network = false
            draft.isDirty.network = false
            draft.error = null
          })
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to save network preferences'
            draft.loading.network = false
          })
        }
      },
      
      saveApp: async () => {
        const state = get()
        if (!state.isDirty.app) return
        
        set((draft) => { draft.loading.app = true })
        
        try {
          const fullPreferences = state._getFullPreferences()
          await PreferencesService.SetPreferences(fullPreferences)
          
          set((draft) => {
            draft.loading.app = false
            draft.isDirty.app = false
            draft.error = null
          })
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to save app preferences'
            draft.loading.app = false
          })
        }
      },
      
      saveServer: async () => {
        const state = get()
        if (!state.isDirty.server) return
        
        set((draft) => { draft.loading.server = true })
        
        try {
          const fullPreferences = state._getFullPreferences()
          await PreferencesService.SetPreferences(fullPreferences)
          
          set((draft) => {
            draft.loading.server = false
            draft.isDirty.server = false
            draft.error = null
          })
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to save server preferences'
            draft.loading.server = false
          })
        }
      },
      
      // Save all dirty sections
      saveAll: async () => {
        const state = get()
        const hasDirtyData = Object.values(state.isDirty).some(Boolean)
        
        if (!hasDirtyData) return
        
        set((draft) => { draft.loading.global = true })
        
        try {
          const fullPreferences = state._getFullPreferences()
          await PreferencesService.SetPreferences(fullPreferences)
          
          set((draft) => {
            draft.loading.global = false
            draft.isDirty = {
              user: false,
              general: false,
              network: false,
              app: false,
              server: false,
            }
            draft.error = null
          })
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to save preferences'
            draft.loading.global = false
          })
        }
      },
      
      // Restore defaults
      restoreDefaults: async () => {
        set((draft) => { 
          draft.loading.restoring = true
          draft.error = null
        })
        
        try {
          const defaultPreferences = await PreferencesService.RestorePreferences()
          
          set((draft) => {
            draft.user = defaultPreferences.user
            draft.general = defaultPreferences.general
            draft.network = defaultPreferences.network
            draft.app = defaultPreferences.app
            draft.server = defaultPreferences.server
            draft.loading.restoring = false
            draft.isDirty = {
              user: false,
              general: false,
              network: false,
              app: false,
              server: false,
            }
          })
        } catch (error) {
          set((draft) => {
            draft.error = error instanceof Error ? error.message : 'Failed to restore defaults'
            draft.loading.restoring = false
          })
        }
      },
      
      // Utility methods
      clearError: () => {
        set((draft) => {
          draft.error = null
        })
      },
      
      resetDirtyState: () => {
        set((draft) => {
          draft.isDirty = {
            user: false,
            general: false,
            network: false,
            app: false,
            server: false,
          }
        })
      },
      
      // Batch updates
      updateMultiple: (updates) => {
        set((state) => {
          if (updates.user && state.user) {
            Object.assign(state.user, updates.user)
            state.isDirty.user = true
          }
          if (updates.general && state.general) {
            Object.assign(state.general, updates.general)
            state.isDirty.general = true
          }
          if (updates.network && state.network) {
            Object.assign(state.network, updates.network)
            state.isDirty.network = true
          }
          if (updates.app && state.app) {
            Object.assign(state.app, updates.app)
            state.isDirty.app = true
          }
          if (updates.server && state.server) {
            Object.assign(state.server, updates.server)
            state.isDirty.server = true
          }
        })
      },
    })),shallow
)

// Convenience hooks for specific sections
export const useUserPreferences = () => usePreferencesStore((state) => ({
  user: state.user,
  updateUser: state.updateUser,
  saveUser: state.saveUser,
  isLoading: state.loading.user,
  isDirty: state.isDirty.user,
}))

export const useGeneralPreferences = () => usePreferencesStore((state) => ({
  general: state.general,
  updateGeneral: state.updateGeneral,
  saveGeneral: state.saveGeneral,
  isLoading: state.loading.general,
}))

export const useNetworkPreferences = () => usePreferencesStore((state) => ({
  network: state.network,
  updateNetwork: state.updateNetwork,
  saveNetwork: state.saveNetwork,
  isLoading: state.loading.network,
  isDirty: state.isDirty.network,
}))

export const useAppPreferences = () => usePreferencesStore((state) => ({
  app: state.app,
  updateApp: state.updateApp,
  saveApp: state.saveApp,
  isLoading: state.loading.app,
  isDirty: state.isDirty.app,
}))

export const useServerPreferences = () => usePreferencesStore((state) => ({
  server: state.server,
  updateServer: state.updateServer,
  saveServer: state.saveServer,
  isLoading: state.loading.server,
  isDirty: state.isDirty.server,
}))

export const useLoadPreferences = () => usePreferencesStore((state) => ({
  loadPreferences: state.loadPreferences,
  // isLoading: state.loading.global,
}))