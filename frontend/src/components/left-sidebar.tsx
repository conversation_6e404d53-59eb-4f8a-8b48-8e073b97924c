"use client";

import {
  Sidebar,
  SidebarItems,
  SidebarItemGroup,
  SidebarItem, Avatar
} from "flowbite-react";
import { useTranslation } from "react-i18next";
import {
  ArrowDownToBracket,
  Cog,
  Search,
  FilePen,
} from "flowbite-react-icons/outline";
import { eventBus } from "../lib/event-bus";
import { ArrowsRepeat } from "flowbite-react-icons/outline";
import { HorizontalDivider } from "./horizontal-divider";
const LeftSidebar = () => {
  // const t = useTranslations()
  // const pathname = usePathname()
  // const locale = useLocale()
  // const windowSize = useWindowResize()
  // const [isLg, setIsLg] = useState(true)
  // const [isInstallDesktop, setIsInstallDesktop] = useState(true)
  const { t } = useTranslation();
  const otherMenu = [
    { icon: ArrowDownToBracket, title: t("front.single"), href: "/" },
    {
      icon: ArrowDownToBracket,
      title: t("front.playlist"),
      href: "/playlist",
    },
    {
      icon: ArrowDownToBracket,
      title: t("front.bulk"),
      href: "/bulk",
    },
    {
      icon: Search,
      title: t("front.universal"),
      href: "/universal",
    },
    {
      icon: ArrowsRepeat,
      title: t("front.converter"),
      href: "/converter",
    },
    {
      icon: FilePen,
      title: t("front.md5"),
      href: "/md5",
    },
  ];
  const audioVideoTools = [
    {
      icon: ArrowDownToBracket,
      title: t("front.md5"),
      href: "/md5-hash-changer",
    },
    {
      icon: ArrowDownToBracket,
      title: t("front.audio"),
      href: "/video-to-audio",
      extraHref: ["/mp4-to-mp3", "/mp4-to-wav"],
    },
  ];

  return (
    <Sidebar
      className=" shrink-0 hidden md:block border-r h-full border-gray-200"
      theme={{
        root: { inner: "bg-gray-50 py-6 px-3" },
        item: {
          base: "group bg-gray-50 hover:bg-gray-200 hover:text-primary-600",
          active: "bg-gray-200 text-primary-600",
          icon: {
            base: "group-hover:text-primary-600",
            active: "text-primary-600",
          },
        },
      }}
    >
      <SidebarItems className="relative h-full w-full">
        <SidebarItemGroup>
          {otherMenu.map((item) => (
            <SidebarItem key={item.title} icon={item.icon} href={item.href}>
              {item.title}
            </SidebarItem>
          ))}
        </SidebarItemGroup>
        <SidebarItemGroup className="items-end justify-end absolute bottom-0 left-0 w-full border-none">
          <SidebarItem
            icon={Cog}
            onClick={() => eventBus.emit("settings-modal", { open: true })}
          >
            settings
          </SidebarItem>
          <HorizontalDivider />
          <SidebarItem className="">
            <div className="flex size-full  gap-2">
              <Avatar img="images/logo-universal.png" />
              <div className="flex gap-2 items-center">
                <p className="text-gray-900 font-semibold">some name</p>
              </div>
            </div>
          </SidebarItem>
        </SidebarItemGroup>
      </SidebarItems>
    </Sidebar>
  );
};

export default LeftSidebar;
