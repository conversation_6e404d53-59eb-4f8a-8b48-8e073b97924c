import { But<PERSON> } from "flowbite-react";
import { Link as LinkIcon } from "flowbite-react-icons/outline";

export const SingleDownloadBottomSection = () => {
  return (
    <div className="bg-gray-50  shadow-sm overflow-y-auto  rounded-lg flex grow flex-col gap-4 items-center justify-center">
      <LinkIcon
        size={96}
        className=" text-gray-500 bg-gray-100 rounded-full p-4"
      />
      <p className="text-gray-900 text-center">尚未提取任何链接</p>
      <p className="text-gray-500 text-center">
        粘贴主页、频道或播放列表的链接地址至上方输入框并单击“提取”按钮即可获取资源
      </p>
      <div>
        <span>支持的平台</span>
        <span>YouTube</span>
        <span>TikTok</span>
        <span>Instagram</span>
        <span>Facebook</span>
      </div>
    </div>
  );
};
