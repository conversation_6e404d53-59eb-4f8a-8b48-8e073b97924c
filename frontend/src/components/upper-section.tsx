import { Button } from "flowbite-react";
import { useTranslation } from "react-i18next";
import { LinkInput } from "./link-input";
import { FormOptions, useForm } from "@tanstack/react-form";

type SingleDownloadUpperSectionProps = {
  title:string
};
  
export const SingleDownloadUpperSection = ({ title }: SingleDownloadUpperSectionProps) => {
  const { t } = useTranslation();
  const form = useForm({
    defaultValues:{
      link: "",
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  });
  return (
    <div className="flex flex-col gap-4">
      <div className="mb-4">
        <h1 className="text-2xl font-bold mb-2">{title}</h1>
      </div>
      <form className="contents" onSubmit={form.handleSubmit}>
       {
        <form.Field name="link">
          {(field) => (
            <LinkInput    value={field.state.value}
            onChange={(e) => field.handleChange(e.target.value)} />
          )}
        </form.Field>
       }
        <Button className="w-fit mx-auto">
          {t("front.extractor.download")}
        </Button>
      </form>
    </div>
  );
};
