import { createTheme, ThemeProvider } from "flowbite-react";
import React from "react";
import { PropsWithChildren } from "react";

const customTheme = createTheme({
  navbar: {
    root: {
      base: "mx-auto max-w-screen-2xl bg-white px-4 py-3 sm:px-4",
    },
    collapse: {
      base: "w-full lg:block lg:w-auto",
    },
    link: {
      base: "block py-2 pl-3 pr-4 lg:p-0",
      active: {
        on: "bg-blue-700 text-white md:bg-transparent md:text-blue-700",
        off: "border-b border-gray-100 text-gray-700 hover:bg-gray-50 lg:border-0 lg:hover:bg-transparent lg:hover:text-blue-700",
      },
      disabled: {
        on: "text-gray-400 hover:cursor-not-allowed",
        off: "",
      },
    },
    toggle: {
      base: "inline-flex items-center rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 lg:hidden",
      icon: "h-6 w-6 shrink-0",
    },
  },
  alert: {
    closeButton: {
      color: {
        info: "bg-blue-50 hover:bg-blue-100 focus:ring-blue-400",
        gray: "bg-gray-50 text-gray-500 hover:bg-gray-200 focus:ring-gray-400",
        green: "bg-gray-50 text-gray-500 hover:bg-gray-200 focus:ring-gray-400",
      },
    },
    color: {
      info: "border-blue-500 bg-blue-50 text-blue-700",
      gray: "border-gray-500 bg-gray-50 text-gray-700",
      green: "border-gray-600 bg-green-50 text-green-700",
    },
  },
  badge: {
    root: {
      color: {
        info: "bg-blue-100 text-blue-800 group-hover:bg-blue-200",
      },
      size: {
        xs: "px-2 py-1 text-xs",
      },
    },
  },
  button: {
    base: "group focus:z-10 cursor-pointer focus:ring-0",
    grouped: "focus:ring-0",
    color: {
      default: "bg-primary-700 text-white hover:bg-primary-800",
      failure:
        "border border-transparent bg-red-700 text-white enabled:hover:bg-red-800",
      gray: ":ring-blue-700 border border-gray-200 bg-white text-gray-900 focus:text-primary-700 enabled:hover:bg-gray-100 enabled:hover:text-primary-700",
      info: "bg-blue-700 text-white hover:bg-blue-800",
      blue: "border border-transparent bg-blue-700 text-white enabled:hover:bg-blue-800",
      light:
        "border border-gray-300 bg-white text-gray-900 enabled:hover:bg-gray-100",
      red: "border border-red-300 bg-white text-red-900 enabled:hover:bg-red-100",
      solidred:
        "border border-transparent bg-red-700 text-white enabled:hover:bg-red-800",
      stone: "bg-gray-100 text-gray-900 hover:bg-primary-700 hover:text-white",
    },
    outlineColor: {
      default: "border-primary-700",
    },
  },
  checkbox: {
    base: "h-4 w-4 rounded border border-gray-300 bg-gray-100 text-primary-700 focus:ring-primary-700 focus:ring-0 focus:ring-offset-0",
    color: {
      default: "text-primary-700 focus:ring-primary-700",
    },
  },
  datepicker: {
    popup: {
      footer: {
        button: {
          base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-blue-300",
          today: "bg-blue-700 text-white hover:bg-blue-800",
        },
      },
    },
    views: {
      days: {
        items: {
          item: {
            selected: "bg-blue-700 text-white hover:bg-blue-600",
          },
        },
      },
      months: {
        items: {
          item: {
            selected: "bg-blue-700 text-white hover:bg-blue-600",
          },
        },
      },
      years: {
        items: {
          item: {
            selected: "bg-blue-700 text-white hover:bg-blue-600",
          },
        },
      },
      decades: {
        items: {
          item: {
            selected: "bg-blue-700 text-white hover:bg-blue-600",
          },
        },
      },
    },
  },
  progress: {
    color: {
      blue: "bg-blue-600",
    },
  },
  radio: {
    base: "h-4 w-4 border border-gray-300 text-primary-700 focus:ring-primary-700 focus:ring-0 focus:ring-offset-0 cursor-pointer disabled:cursor-not-allowed",
    color: {
      default: "text-primary-700 focus:ring-primary-600",
    },
  },
  select: {
    field: {
      select: {
        colors: {
          gray: "border-gray-300 bg-gray-50 text-gray-900 focus:border-primary-700 focus:ring-primary-700",
        },
      },
    },
  },
  textarea: {
    colors: {
      gray: "border-gray-300 bg-gray-50 text-gray-900 placeholder-gray-400 focus:border-primary-700 focus:ring-primary-700",
    },
  },
  textInput: {
    field: {
      input: {
        colors: {
          gray: "border-gray-300 bg-gray-50 text-gray-900 placeholder-gray-400 focus:border-primary-700 focus:ring-primary-700",
          info: "border-blue-500 bg-blue-50 text-blue-900 placeholder-blue-700 focus:border-blue-500 focus:ring-blue-500",
        },
        withIcon: {
          on: "pl-10",
        },
      },
    },
  },
  tooltip: {
    animation: "delay-500",
    hidden: "invisible opacity-0 transition-none",
  },
  spinner: {
    color: {
      default: "fill-primary-800",
    },
  },
  modal: {
    header: {
      base: "py-4 px-6 border-gray-200",
      close: {
        base: "cursor-pointer hover:bg-inherit focus-visible:outline-none",
      },
    },
    body: {
      base: "p-5 focus-visible:outline-none",
    },
    content: {
      base: "focus-visible:outline-none",
    },
  },
  pagination: {
    pages: {
      base: "mt-0",
      previous: {
        base: "py-1.5 cursor-pointer disabled:cursor-not-allowed",
      },
      next: {
        base: "py-1.5 cursor-pointer disabled:cursor-not-allowed",
      },
      selector: {
        base: "py-1.5 cursor-pointer",
        active: "bg-primary-700/20 text-primary-700",
      },
    },
  },
});

export function CustomTheme({ children }: PropsWithChildren) {
  return <ThemeProvider theme={customTheme}>{children}</ThemeProvider>;
}
