import { TextInput } from "flowbite-react";
import { FileCopyAlt as FileCopyAltIcon } from "flowbite-react-icons/outline";
import { useTranslation } from "react-i18next";

type LinkInputProps = {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
};

export const LinkInput = ({ value, onChange, placeholder }: LinkInputProps) => {
  const { t } = useTranslation();
  return (
    <div className="relative">
      <TextInput className="" placeholder={placeholder || t("front.extractor.placeholder")} value={value} onChange={onChange} />
      <FileCopyAltIcon onClick={() => {}} className="hover:cursor-pointer absolute right-3 top-1/2 -translate-y-1/2 text-gray-500" />
    </div>
  );
};
