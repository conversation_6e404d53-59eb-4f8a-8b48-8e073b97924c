import { Mo<PERSON>, <PERSON>dal<PERSON><PERSON>, ModalHeader } from "flowbite-react";
import { eventBus, Events } from "@/lib/event-bus";
import { useState, useEffect } from "react";
import { cn } from "@/utils/cn";
import { useTranslation } from "react-i18next";
import type { SettingsTabProps } from "@/components/settings-modal/types";
import { SettingsGeneralTab } from "@/components/settings-modal/tabs/general/general-tab";
import { SettingsTabContent } from "@/components/settings-modal/tabs/settings-tab-content";

export const SettingsModal = () => {
  const [open, setOpen] = useState(false);
  useEffect(() => {
    const handleSettingsModal = (event: Events["settings-modal"]) => {
      setOpen(event.open);
    };
    eventBus.on("settings-modal", handleSettingsModal);
    return () => {
      eventBus.off("settings-modal", handleSettingsModal);
    };
  }, []);

  const [activeTab, setActiveTab] = useState("general");
  return (
    <Modal show={open} size="6xl" onClose={() => setOpen(false)}>
      <ModalHeader className="border-gray-200">
        <span className="text-lg text-gray-900 font-semibold">settings</span>
      </ModalHeader>
      <ModalBody className="p-0">
        <div className="flex size-full">
          <section className="w-52 h-full py-3">
            <SettingsTab
              isActive={activeTab === "general"}
              setActiveTab={setActiveTab}
              tabKey="general"
              titleKey="settings.tabs.general"
            />
            <SettingsTab
              isActive={activeTab === "network"}
              setActiveTab={setActiveTab}
              tabKey="network"
              titleKey="settings.tabs.network"
            />
            <SettingsTab
              isActive={activeTab === "permission"}
              setActiveTab={setActiveTab}
              tabKey="permission"
              titleKey="settings.tabs.permission"
            />
            <SettingsTab
              isActive={activeTab === "about"}
              setActiveTab={setActiveTab}
              tabKey="about"
              titleKey="settings.tabs.about"
            />
          </section>
          <section className="flex-1 border-l border-gray-200">
            <SettingsTabContent isActive={activeTab === "general"}>
              <SettingsGeneralTab />
            </SettingsTabContent>
            <SettingsTabContent isActive={activeTab === "network"}>
              <SettingsNetworkTab />
            </SettingsTabContent>
            <SettingsTabContent isActive={activeTab === "permission"}>
              <SettingsPermissionTab />
            </SettingsTabContent>
            <SettingsTabContent isActive={activeTab === "about"}>
              <SettingsAboutTab />
            </SettingsTabContent>
          </section>
        </div>
      </ModalBody>
    </Modal>
  );
};

function SettingsTab({
  isActive,
  setActiveTab,
  tabKey,
  titleKey,
}: SettingsTabProps) {
  const { t } = useTranslation();
  return (
    <div
      className={cn(
        "relative items-center hover:cursor-pointer",
        isActive && "bg-gray-100"
      )}
      onClick={() => setActiveTab(tabKey)}
    >
      <p className="px-6 py-3">{t(titleKey)}</p>
      {isActive && (
        <div className="absolute w-1 h-full bg-primary-700 top-0 lefp-0"></div>
      )}
    </div>
  );
}

function SettingsNetworkTab() {
  return <div>SettingsNetworkTab</div>;
}

function SettingsPermissionTab() {
  return <div>SettingsPermissionTab</div>;
}

function SettingsAboutTab() {
  return <div>SettingsAboutTab</div>;
}
