import { Seperator } from "@/components/seperator";
import { Button, ToggleSwitch } from "flowbite-react";

export function NetworkTab() {
  return (
    <div className="p-6 flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <p className="font-semibold text-lg">Enable Proxy</p>
        <ToggleSwitch checked={true} onChange={() => {}} />
      </div>
      <Seperator />
      <div className="flex items-center justify-between">
        <p className="font-semibold text-lg">Proxy Test</p>
        <Button>Start Test</Button>
      </div>
      
    </div>
  );
}
