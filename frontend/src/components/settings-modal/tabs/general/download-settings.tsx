import { Button, TextInput } from "flowbite-react";
import { Folder as FolderIcon } from "flowbite-react-icons/outline";
import { PickDirectory } from "@/../wailsjs/go/services/DialogService.js";
import { useGeneralPreferences } from "@/store/preference";
import { useEnsurePreferenceLoaded } from "@/hooks/useEnsurePreferenceLoaded";
import { useTranslation } from "react-i18next";

export function DownloadSetting() {
  const { general, updateGeneral, saveGeneral } = useGeneralPreferences();
  const { t } = useTranslation();
  useEnsurePreferenceLoaded(general);
  const handleSelectFolder = async () => {
    const selectedPath = await PickDirectory(
      "Select Download Directory",
      general?.downloadDir || ""
    );

    if (selectedPath) {
      updateGeneral({ downloadDir: selectedPath });
      saveGeneral();
    }
  };
  if (!general) {
    return (
      <div className="flex gap-2 flex-col">
        <span className="text-sm">{t("settings.general.downloadDir")}</span>
        <div className="relative overflow-hidden">
          <TextInput disabled value="Loading..." />
        </div>
      </div>
    );
  }

  return (
    <div className="flex gap-2 flex-col">
      <span className="text-sm">{t("settings.general.downloadDir")}</span>
      <div className="relative overflow-hidden">
        <TextInput
          disabled
          value={general.downloadDir || t("settings.general.noDownloadDir")}
          placeholder={t("settings.general.selectDownloadDir")}
        />
        <div className="absolute right-0 top-1/2 -translate-y-1/2 flex gap-2 items-center justify-center">
          <FolderIcon size={16} />
          <Button
            color="gray"
            className="rounded-lg rounded-l-none -z-10"
            onClick={handleSelectFolder}
            // disabled={isLoading}
          >
            {t("settings.general.changeFolder")}
          </Button>
        </div>
      </div>
    </div>
  );
}
