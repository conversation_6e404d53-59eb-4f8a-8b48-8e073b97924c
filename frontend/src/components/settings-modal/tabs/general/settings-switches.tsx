import { ToggleSwitch } from "flowbite-react";
import { useGeneralPreferences } from "../../../../store/preference";
import { types } from "../../../../../wailsjs/go/models";
import { useTranslation } from "react-i18next";
export function SettingsSwitches() {
    const { general, updateGeneral, saveGeneral, isLoading } = useGeneralPreferences();
    const { t } = useTranslation();

    const handleToggle = (key: keyof types.PreferencesGeneral, value: boolean) => {
      if (!general) return;
      updateGeneral({ [key]: value });
      saveGeneral();
    };

    if (!general) {
      return (
        <div className="flex flex-col">
          <div className="text-gray-500 p-4 text-center">{t("global.loading")}</div>
        </div>
      );
    }

    return (
      <div className="flex flex-col -mt-2">
        <Switch
          title={t("settings.general.authorNameCreateDirectory")}
          checked={general.authorNameCreateDirectory || false}
          onChange={(checked) => handleToggle('authorNameCreateDirectory', checked)}
          disabled={isLoading}
        />
        <Switch
          title={t("settings.general.postClassifyCreateDirectory")}
          checked={general.postClassifyCreateDirectory || false}
          onChange={(checked) => handleToggle('postClassifyCreateDirectory', checked)}
          disabled={isLoading}
        />
        <Switch 
          title={t("settings.general.addSerialNumber")} 
          checked={general.addSerialNumber || false} 
          onChange={(checked) => handleToggle('addSerialNumber', checked)}
          disabled={isLoading}
        />
        <Switch 
          title={t("settings.general.saveAutoAlterMD5")} 
          checked={general.saveAutoAlterMD5 || false} 
          onChange={(checked) => handleToggle('saveAutoAlterMD5', checked)}
          disabled={isLoading}
        />
      </div>
    );
  }
  
  export function Switch({
    title,
    checked,
    onChange,
    disabled = false,
  }: {
    title: string;
    checked: boolean;
    onChange: (checked: boolean) => void;
    disabled?: boolean;
  }) {
    return (
      <div className="flex items-center justify-between gap-2 py-2">
        <span className={disabled ? "text-gray-400" : ""}>{title}</span>
        <ToggleSwitch 
          checked={checked} 
          onChange={onChange} 
          disabled={disabled}
        />
      </div>
    );
  }
  