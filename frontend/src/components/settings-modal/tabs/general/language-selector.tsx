import { Select } from "flowbite-react";
import { useGeneralPreferences } from "@/store/preference";
import { useTranslation } from "react-i18next";
export function LanguageSelect() {
    const { general, updateGeneral, saveGeneral, isLoading } = useGeneralPreferences();
    const { t } = useTranslation();

    const handleLanguageChange = (language: string) => {
      updateGeneral({ language });
      saveGeneral();
    };

    if (!general) {
      return (
        <Select className="w-40" disabled>
          <option>{t("global.loading")}</option>
        </Select>
      );
    }

    return (
      <Select 
        className="w-40"
        value={general.language || "en"}
        onChange={(e) => handleLanguageChange(e.target.value)}
        disabled={isLoading}
      >
        <option value="en">English</option>
        <option value="zh">中文</option>
        <option value="ja">日本語</option>
        <option value="es">Español</option>
        <option value="fr">Français</option>
        <option value="de">Deutsch</option>
        <option value="ko">한국어</option>
        <option value="ru">Русский</option>
      </Select>
    );
  }
  
  
  
  