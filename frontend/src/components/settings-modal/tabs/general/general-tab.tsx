import { Select } from "flowbite-react";
import { DownloadSetting } from "@/components/settings-modal/tabs/general/download-settings";
import { SettingSection, SettingSectionTitle } from "@/components/settings-modal/settings-layout";
import { SettingsSwitches } from "@/components/settings-modal/tabs/general/settings-switches";
import { LanguageSelect } from "@/components/settings-modal/tabs/general/language-selector";
import { useGeneralPreferences } from "@/store/preference";
import { useEnsurePreferenceLoaded } from "@/hooks/useEnsurePreferenceLoaded";
import { useTranslation } from "react-i18next";
import { SUPPORTED_LANGUAGES } from "@/constant/language";

export function SettingsGeneralTab() {
    const { general, updateGeneral, saveGeneral, isLoading } = useGeneralPreferences();
    const { t } = useTranslation();
    useEnsurePreferenceLoaded(general);

    const handleAudioLanguageChange = (value: string) => {
      updateGeneral({ audioTrackDefaultLanguage: value });
      // Auto-save after a short delay or save manually
      saveGeneral();
    };

    const handleVideoQualityChange = (value: string) => {
      updateGeneral({ videoDefaultResolution: value });
      // Auto-save after a short delay or save manually
      saveGeneral();
    };

    return (
      <div className="p-6 flex flex-col gap-6">
        <SettingSection>
          <SettingSectionTitle title={t("settings.general.languageSetting")} />
          <LanguageSelect />
        </SettingSection>
        <SettingSection>
          <SettingSectionTitle title={t("settings.general.downloadSetting")} />
          <DownloadSetting />
          <div className="flex flex-col md:flex-row w-full *:w-full gap-6"> 
            <div className="w-full flex flex-col gap-2">
              <span className="text-sm">{t("settings.general.audioLanguageSetting")}</span>
              <Select 
                value={general?.audioTrackDefaultLanguage || "default"}
                onChange={(e) => handleAudioLanguageChange(e.target.value)}
                disabled={isLoading}
              >
               {
                SUPPORTED_LANGUAGES.map((language) => (
                  <option key={language} value={language}>{t(`language.${language}`)}</option>
                ))
               }
              </Select>
            </div>
            <div className="w-full flex flex-col gap-2">
              <span className="text-sm">{t("settings.general.videoQualitySetting")}</span>
              <Select 
                value={general?.videoDefaultResolution || "default"}
                onChange={(e) => handleVideoQualityChange(e.target.value)}
                disabled={isLoading}
              >
                <option value="default">{t("settings.general.videoQualitySetting")}</option>
                <option value="4k">4K (2160p)</option>
                <option value="1440p">2K (1440p)</option>
                <option value="1080p">Full HD (1080p)</option>
                <option value="720p">HD (720p)</option>
                <option value="480p">SD (480p)</option>
                <option value="360p">360p</option>
                <option value="best">{t("settings.general.options.bestQuality")}</option>
                <option value="worst">{t("settings.general.options.worstQuality")}</option>
              </Select>
            </div>
          </div>
        </SettingSection>
        <SettingsSwitches />
      </div>
    );
  }