import React from "react";
import { createRoot } from "react-dom/client";
import { createRouter, RouterProvider } from "@tanstack/react-router";
import "./style.css";
import { routeTree } from "./routeTree.gen";
import "./lib/i18n";
const container = document.getElementById("root");
const router = createRouter({
  routeTree,
});
const root = createRoot(container!);

root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>
);
