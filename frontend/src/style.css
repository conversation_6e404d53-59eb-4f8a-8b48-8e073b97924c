@import "tailwindcss";
@source "../.flowbite-react/class-list.json";
@plugin "flowbite/plugin";
@plugin "@tailwindcss/typography";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

html,
body {
  font-size: 16px;
  color: #111928;
}

@theme {
  --color-primary-50: #f3fae8;
  --color-primary-100: #e5f5d2;
  --color-primary-600: #3b7a21;
  --color-primary-700: #56ab2f;
  --color-primary-800: #3e8c1f;
  --color-primary-900: #1d7b00;
  --spacing-two-characters: 2rem;

  --animate-two-characters-carousel: two-characters-carousel 10s linear infinite;
  @keyframes two-characters-carousel {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-100% - var(--spacing-two-characters)));
    }
  }
}

@utility scrollbar-hidden {
  &::-webkit-scrollbar {
    display: none;
  }
}

@layer utilities {
  .text-fill-transparent {
    -webkit-text-fill-color: transparent;
  }
}

.marker li::marker {
  unicode-bidi: isolate;
  font-variant-numeric: tabular-nums;
  text-transform: none;
  text-indent: 0px !important;
  text-align: start !important;
  text-align-last: auto !important;
}
