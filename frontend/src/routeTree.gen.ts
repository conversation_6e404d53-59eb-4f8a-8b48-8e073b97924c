/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as IndexRouteImport } from './routes/index'
import { Route as UniversalIndexRouteImport } from './routes/universal/index'
import { Route as PlaylistIndexRouteImport } from './routes/playlist/index'
import { Route as Md5IndexRouteImport } from './routes/md5/index'
import { Route as ConverterIndexRouteImport } from './routes/converter/index'
import { Route as BulkIndexRouteImport } from './routes/bulk/index'

const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const UniversalIndexRoute = UniversalIndexRouteImport.update({
  id: '/universal/',
  path: '/universal/',
  getParentRoute: () => rootRouteImport,
} as any)
const PlaylistIndexRoute = PlaylistIndexRouteImport.update({
  id: '/playlist/',
  path: '/playlist/',
  getParentRoute: () => rootRouteImport,
} as any)
const Md5IndexRoute = Md5IndexRouteImport.update({
  id: '/md5/',
  path: '/md5/',
  getParentRoute: () => rootRouteImport,
} as any)
const ConverterIndexRoute = ConverterIndexRouteImport.update({
  id: '/converter/',
  path: '/converter/',
  getParentRoute: () => rootRouteImport,
} as any)
const BulkIndexRoute = BulkIndexRouteImport.update({
  id: '/bulk/',
  path: '/bulk/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/bulk': typeof BulkIndexRoute
  '/converter': typeof ConverterIndexRoute
  '/md5': typeof Md5IndexRoute
  '/playlist': typeof PlaylistIndexRoute
  '/universal': typeof UniversalIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/bulk': typeof BulkIndexRoute
  '/converter': typeof ConverterIndexRoute
  '/md5': typeof Md5IndexRoute
  '/playlist': typeof PlaylistIndexRoute
  '/universal': typeof UniversalIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/bulk/': typeof BulkIndexRoute
  '/converter/': typeof ConverterIndexRoute
  '/md5/': typeof Md5IndexRoute
  '/playlist/': typeof PlaylistIndexRoute
  '/universal/': typeof UniversalIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/bulk' | '/converter' | '/md5' | '/playlist' | '/universal'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/bulk' | '/converter' | '/md5' | '/playlist' | '/universal'
  id:
    | '__root__'
    | '/'
    | '/bulk/'
    | '/converter/'
    | '/md5/'
    | '/playlist/'
    | '/universal/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  BulkIndexRoute: typeof BulkIndexRoute
  ConverterIndexRoute: typeof ConverterIndexRoute
  Md5IndexRoute: typeof Md5IndexRoute
  PlaylistIndexRoute: typeof PlaylistIndexRoute
  UniversalIndexRoute: typeof UniversalIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/universal/': {
      id: '/universal/'
      path: '/universal'
      fullPath: '/universal'
      preLoaderRoute: typeof UniversalIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/playlist/': {
      id: '/playlist/'
      path: '/playlist'
      fullPath: '/playlist'
      preLoaderRoute: typeof PlaylistIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/md5/': {
      id: '/md5/'
      path: '/md5'
      fullPath: '/md5'
      preLoaderRoute: typeof Md5IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/converter/': {
      id: '/converter/'
      path: '/converter'
      fullPath: '/converter'
      preLoaderRoute: typeof ConverterIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/bulk/': {
      id: '/bulk/'
      path: '/bulk'
      fullPath: '/bulk'
      preLoaderRoute: typeof BulkIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  BulkIndexRoute: BulkIndexRoute,
  ConverterIndexRoute: ConverterIndexRoute,
  Md5IndexRoute: Md5IndexRoute,
  PlaylistIndexRoute: PlaylistIndexRoute,
  UniversalIndexRoute: UniversalIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
