["-4px", "-bottom-1", "-left-1", "-right-1", "-space-x-4", "-top-1", "2xl", "3xl", "4xl", "5xl", "6xl", "7xl", "absolute", "after:absolute", "after:bg-white", "after:border", "after:border-gray-300", "after:border-transparent", "after:h-4", "after:h-5", "after:h-6", "after:left-0.5", "after:rounded-full", "after:top-0.5", "after:transition-all", "after:translate-x-full", "after:w-4", "after:w-5", "after:w-6", "appearance-none", "bg-[length:0.75em_0.75em]", "bg-[position:right_12px_center]", "bg-arrow-down-icon", "bg-blue-100", "bg-blue-700", "bg-cyan-100", "bg-cyan-50", "bg-cyan-500", "bg-cyan-600", "bg-cyan-700", "bg-dark-50", "bg-gray-100", "bg-gray-200", "bg-gray-300", "bg-gray-400", "bg-gray-50", "bg-gray-500", "bg-gray-600", "bg-gray-700", "bg-gray-800", "bg-gray-900", "bg-gray-900/50", "bg-green-100", "bg-green-400", "bg-green-50", "bg-green-500", "bg-green-600", "bg-green-700", "bg-indigo-100", "bg-indigo-400", "bg-indigo-700", "bg-light-50", "bg-lime-100", "bg-lime-400", "bg-lime-700", "bg-no-repeat", "bg-pink-100", "bg-pink-600", "bg-pink-700", "bg-primary-700", "bg-purple-100", "bg-purple-50", "bg-purple-700", "bg-red-100", "bg-red-400", "bg-red-50", "bg-red-700", "bg-teal-100", "bg-teal-400", "bg-teal-700", "bg-transparent", "bg-white", "bg-yellow-100", "bg-yellow-400", "bg-yellow-50", "bg-yellow-600", "block", "border", "border-2", "border-b", "border-b-0", "border-blue-700", "border-cyan-500", "border-cyan-700", "border-gray-200", "border-gray-300", "border-gray-700", "border-gray-800", "border-green-500", "border-green-700", "border-indigo-700", "border-l-0", "border-lime-700", "border-pink-700", "border-primary-700", "border-purple-700", "border-r-0", "border-red-500", "border-red-700", "border-t", "border-teal-700", "border-white", "border-yellow-400", "border-yellow-500", "bottom-center", "bottom-left", "bottom-right", "center-left", "center-right", "cursor-not-allowed", "cursor-pointer", "dark:bg-blue-200", "dark:bg-blue-600", "dark:bg-cyan-100", "dark:bg-cyan-200", "dark:bg-cyan-600", "dark:bg-cyan-900", "dark:bg-dark-900", "dark:bg-gray-400", "dark:bg-gray-600", "dark:bg-gray-700", "dark:bg-gray-800", "dark:bg-gray-900", "dark:bg-gray-900/80", "dark:bg-green-100", "dark:bg-green-200", "dark:bg-green-600", "dark:bg-green-900", "dark:bg-indigo-200", "dark:bg-indigo-600", "dark:bg-light-900", "dark:bg-lime-200", "dark:bg-lime-600", "dark:bg-pink-200", "dark:bg-pink-600", "dark:bg-primary-600", "dark:bg-purple-200", "dark:bg-purple-600", "dark:bg-purple-900", "dark:bg-red-100", "dark:bg-red-200", "dark:bg-red-600", "dark:bg-red-900", "dark:bg-teal-200", "dark:bg-teal-600", "dark:bg-yellow-100", "dark:bg-yellow-200", "dark:bg-yellow-600", "dark:bg-yellow-900", "dark:border-blue-500", "dark:border-cyan-400", "dark:border-cyan-500", "dark:border-gray-600", "dark:border-gray-700", "dark:border-gray-800", "dark:border-green-400", "dark:border-green-600", "dark:border-indigo-600", "dark:border-lime-600", "dark:border-none", "dark:border-pink-600", "dark:border-primary-600", "dark:border-purple-600", "dark:border-red-400", "dark:border-red-600", "dark:border-teal-600", "dark:border-yellow-300", "dark:border-yellow-400", "dark:focus:border-cyan-500", "dark:focus:border-green-500", "dark:focus:border-primary-500", "dark:focus:border-red-500", "dark:focus:border-yellow-500", "dark:focus:ring-blue-800", "dark:focus:ring-cyan-500", "dark:focus:ring-cyan-800", "dark:focus:ring-gray-700", "dark:focus:ring-gray-800", "dark:focus:ring-green-500", "dark:focus:ring-green-800", "dark:focus:ring-indigo-800", "dark:focus:ring-lime-800", "dark:focus:ring-pink-800", "dark:focus:ring-primary-500", "dark:focus:ring-primary-800", "dark:focus:ring-purple-800", "dark:focus:ring-red-500", "dark:focus:ring-red-800", "dark:focus:ring-teal-800", "dark:focus:ring-yellow-500", "dark:focus:ring-yellow-900", "dark:group-focus:ring-blue-800", "dark:group-focus:ring-cyan-800", "dark:group-focus:ring-gray-800", "dark:group-focus:ring-green-800", "dark:group-focus:ring-indigo-800", "dark:group-focus:ring-lime-800", "dark:group-focus:ring-pink-800", "dark:group-focus:ring-primary-800", "dark:group-focus:ring-purple-800", "dark:group-focus:ring-red-800", "dark:group-focus:ring-teal-800", "dark:group-focus:ring-yellow-800", "dark:group-hover:text-white", "dark:hover:bg-blue-300", "dark:hover:bg-blue-700", "dark:hover:bg-cyan-300", "dark:hover:bg-cyan-700", "dark:hover:bg-gray-500", "dark:hover:bg-gray-600", "dark:hover:bg-gray-700", "dark:hover:bg-green-300", "dark:hover:bg-green-700", "dark:hover:bg-indigo-300", "dark:hover:bg-indigo-700", "dark:hover:bg-lime-300", "dark:hover:bg-lime-700", "dark:hover:bg-pink-300", "dark:hover:bg-pink-700", "dark:hover:bg-primary-700", "dark:hover:bg-purple-300", "dark:hover:bg-purple-700", "dark:hover:bg-red-300", "dark:hover:bg-red-700", "dark:hover:bg-teal-300", "dark:hover:bg-teal-700", "dark:hover:bg-yellow-300", "dark:hover:bg-yellow-400", "dark:hover:border-blue-700", "dark:hover:border-cyan-700", "dark:hover:border-gray-600", "dark:hover:border-gray-700", "dark:hover:border-green-700", "dark:hover:border-indigo-700", "dark:hover:border-lime-700", "dark:hover:border-pink-700", "dark:hover:border-primary-700", "dark:hover:border-purple-700", "dark:hover:border-red-700", "dark:hover:border-teal-700", "dark:hover:border-yellow-400", "dark:hover:text-white", "dark:placeholder-gray-400", "dark:ring-cyan-800", "dark:ring-gray-400", "dark:ring-gray-500", "dark:ring-gray-800", "dark:ring-green-500", "dark:ring-pink-500", "dark:ring-purple-600", "dark:ring-red-700", "dark:ring-yellow-500", "dark:shadow-sm-light", "dark:text-blue-500", "dark:text-blue-900", "dark:text-cyan-500", "dark:text-cyan-800", "dark:text-cyan-900", "dark:text-gray-100", "dark:text-gray-200", "dark:text-gray-300", "dark:text-gray-400", "dark:text-gray-900", "dark:text-green-500", "dark:text-green-900", "dark:text-indigo-400", "dark:text-indigo-900", "dark:text-lime-500", "dark:text-lime-900", "dark:text-pink-500", "dark:text-pink-900", "dark:text-primary-500", "dark:text-purple-400", "dark:text-purple-900", "dark:text-red-500", "dark:text-red-900", "dark:text-teal-400", "dark:text-teal-900", "dark:text-white", "dark:text-yellow-300", "dark:text-yellow-900", "delay-0", "disabled:cursor-not-allowed", "disabled:opacity-50", "duration-75", "ease-in-out", "first:border-l", "first:border-t-0", "first:mt-0", "first:pt-0", "first:rounded-s-lg", "fixed", "flex", "flex-1", "flex-col", "focus:border-cyan-500", "focus:border-green-500", "focus:border-primary-500", "focus:border-red-500", "focus:border-yellow-500", "focus:outline-none", "focus:ring-1", "focus:ring-2", "focus:ring-4", "focus:ring-blue-300", "focus:ring-cyan-300", "focus:ring-cyan-500", "focus:ring-gray-100", "focus:ring-gray-300", "focus:ring-green-300", "focus:ring-green-500", "focus:ring-indigo-300", "focus:ring-lime-300", "focus:ring-pink-300", "focus:ring-primary-300", "focus:ring-primary-500", "focus:ring-purple-300", "focus:ring-red-300", "focus:ring-red-500", "focus:ring-teal-300", "focus:ring-yellow-300", "focus:ring-yellow-500", "font-bold", "font-medium", "font-normal", "font-semibold", "gap-1", "group", "group-focus:ring-4", "group-focus:ring-blue-300", "group-focus:ring-cyan-300", "group-focus:ring-gray-300", "group-focus:ring-green-300", "group-focus:ring-indigo-300", "group-focus:ring-lime-300", "group-focus:ring-pink-300", "group-focus:ring-primary-300", "group-focus:ring-purple-300", "group-focus:ring-red-300", "group-focus:ring-teal-300", "group-focus:ring-yellow-300", "group-hover:text-gray-900", "h-10", "h-12", "h-2", "h-20", "h-3", "h-3.5", "h-36", "h-5", "h-6", "h-7", "h-8", "h-9", "h-[52px]", "h-auto", "h-fit", "h-full", "h-screen", "hidden", "hover:bg-blue-200", "hover:bg-blue-800", "hover:bg-cyan-200", "hover:bg-cyan-800", "hover:bg-gray-100", "hover:bg-gray-200", "hover:bg-gray-300", "hover:bg-gray-500", "hover:bg-gray-600", "hover:bg-gray-800", "hover:bg-gray-900", "hover:bg-green-200", "hover:bg-green-800", "hover:bg-indigo-200", "hover:bg-indigo-800", "hover:bg-lime-200", "hover:bg-lime-800", "hover:bg-pink-200", "hover:bg-pink-800", "hover:bg-primary-800", "hover:bg-purple-200", "hover:bg-purple-800", "hover:bg-red-200", "hover:bg-red-800", "hover:bg-teal-200", "hover:bg-teal-800", "hover:bg-yellow-200", "hover:bg-yellow-500", "hover:border-blue-800", "hover:border-cyan-800", "hover:border-gray-800", "hover:border-gray-900", "hover:border-green-800", "hover:border-indigo-800", "hover:border-lime-800", "hover:border-pink-800", "hover:border-primary-800", "hover:border-purple-800", "hover:border-red-800", "hover:border-teal-800", "hover:border-yellow-500", "hover:text-gray-900", "hover:text-primary-700", "hover:text-white", "inline-block", "inline-flex", "inset-x-0", "inset-y-0", "invisible", "items-center", "items-end", "items-start", "justify-between", "justify-center", "justify-end", "justify-start", "last:rounded-e-lg", "left-0", "max-h-[90dvh]", "max-w-2xl", "max-w-3xl", "max-w-4xl", "max-w-5xl", "max-w-6xl", "max-w-7xl", "max-w-lg", "max-w-md", "max-w-sm", "max-w-xl", "mb-5", "md:h-auto", "md:h-full", "md:inset-0", "min-w-11", "min-w-9", "min-w-[52px]", "ml-3", "ml-auto", "mr-3", "ms-3", "mt-0.5", "mt-4", "mt-6", "opacity-0", "opacity-50", "overflow-auto", "overflow-hidden", "overflow-x-hidden", "overflow-y-auto", "p-1", "p-1.5", "p-2", "p-2.5", "p-4", "p-5", "p-6", "pl-10", "pl-2.5", "pl-3", "pl-8", "placeholder-cyan-700", "placeholder-gray-500", "placeholder-green-700", "placeholder-red-700", "placeholder-yellow-700", "pointer-events-none", "pr-10", "pr-3", "pt-0", "pt-4", "px-2", "px-3", "px-5", "px-6", "py-0.5", "py-2", "py-4", "relative", "right-0", "ring-2", "ring-cyan-400", "ring-gray-300", "ring-gray-500", "ring-gray-800", "ring-green-500", "ring-pink-500", "ring-purple-500", "ring-red-500", "ring-yellow-300", "rotate-180", "rotate-45", "rounded-b", "rounded-full", "rounded-l-md", "rounded-lg", "rounded-md", "rounded-none", "rounded-r-lg", "rounded-sm", "rounded-t", "rtl:after:-translate-x-full", "rtl:after:right-0.5", "self-center", "shadow-sm", "shadow-xs", "shrink-0", "sm:h-7", "sm:text-base", "sm:text-xs", "space-x-2", "space-x-4", "space-y-2", "sr-only", "text-base", "text-blue-700", "text-blue-800", "text-center", "text-cyan-700", "text-cyan-800", "text-cyan-900", "text-gray-100", "text-gray-400", "text-gray-500", "text-gray-600", "text-gray-700", "text-gray-800", "text-gray-900", "text-green-700", "text-green-800", "text-green-900", "text-indigo-700", "text-indigo-800", "text-left", "text-lime-700", "text-lime-800", "text-pink-700", "text-pink-800", "text-primary-700", "text-purple-700", "text-purple-800", "text-red-700", "text-red-800", "text-red-900", "text-sm", "text-start", "text-teal-700", "text-teal-800", "text-white", "text-xl", "text-xs", "text-yellow-400", "text-yellow-800", "text-yellow-900", "top-0", "top-center", "top-left", "top-right", "transition", "transition-opacity", "w-10", "w-11", "w-16", "w-2", "w-20", "w-3", "w-3.5", "w-36", "w-5", "w-6", "w-64", "w-8", "w-9", "w-[52px]", "w-auto", "w-fit", "w-full", "whitespace-nowrap", "z-10", "z-20", "z-50"]