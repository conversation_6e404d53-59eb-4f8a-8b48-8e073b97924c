import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import { tanstackRouter } from "@tanstack/router-plugin/vite";
import flowbiteReact from "flowbite-react/plugin/vite";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [tanstackRouter({
    target: "react",
    autoCodeSplitting: true,
  }), react(), tailwindcss(), flowbiteReact()],
  resolve: {
    alias: {
        "@": path.resolve(__dirname, "src"),
    },
  },
});