// vite.config.ts
import { defineConfig } from "file:///Users/<USER>/development/code/wails/henghengman-desktop/frontend/node_modules/.pnpm/vite@3.2.11/node_modules/vite/dist/node/index.js";
import react from "file:///Users/<USER>/development/code/wails/henghengman-desktop/frontend/node_modules/.pnpm/@vitejs+plugin-react@2.2.0_vite@3.2.11/node_modules/@vitejs/plugin-react/dist/index.mjs";
var vite_config_default = defineConfig({
  plugins: [react()]
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCIvVXNlcnMvemhmL2RldmVsb3BtZW50L2NvZGUvd2FpbHMvaGVuZ2hlbmdtYW4tZGVza3RvcC9mcm9udGVuZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiL1VzZXJzL3poZi9kZXZlbG9wbWVudC9jb2RlL3dhaWxzL2hlbmdoZW5nbWFuLWRlc2t0b3AvZnJvbnRlbmQvdml0ZS5jb25maWcudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL1VzZXJzL3poZi9kZXZlbG9wbWVudC9jb2RlL3dhaWxzL2hlbmdoZW5nbWFuLWRlc2t0b3AvZnJvbnRlbmQvdml0ZS5jb25maWcudHNcIjtpbXBvcnQge2RlZmluZUNvbmZpZ30gZnJvbSAndml0ZSdcbmltcG9ydCByZWFjdCBmcm9tICdAdml0ZWpzL3BsdWdpbi1yZWFjdCdcblxuLy8gaHR0cHM6Ly92aXRlanMuZGV2L2NvbmZpZy9cbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZyh7XG4gIHBsdWdpbnM6IFtyZWFjdCgpXVxufSlcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFBNFcsU0FBUSxvQkFBbUI7QUFDdlksT0FBTyxXQUFXO0FBR2xCLElBQU8sc0JBQVEsYUFBYTtBQUFBLEVBQzFCLFNBQVMsQ0FBQyxNQUFNLENBQUM7QUFDbkIsQ0FBQzsiLAogICJuYW1lcyI6IFtdCn0K
