# README

## 项目打包

### mac版本
```shell
make build-mac
make copy-mac-libs
```

### windows版本
```shell
make build-windows
```

## 构建项目

### 运行环境要求

* Go >= 1.24
* Node.js >= 20
* pnpm >= 10

### 安装wails

```bash
go install github.com/wailsapp/wails/v2/cmd/wails@latest
```

### 生成Go映射到JS的方法

```shell
make gm # 需要安装make
```

或

```shell
wails generate module
```

### 构建前端代码

```shell
make fi # 需要安装make
```

或

```bash
pnpm install --prefix ./frontend
```

或

```bash
cd frontend
pnpm install
```

### 编译运行开发版本

```bash
wails dev
```
