package types

type DownloadOutputType string

const (
	DownloadOutputTypeVideo DownloadOutputType = "video"
	DownloadOutputTypeAudio DownloadOutputType = "audio"
	DownloadOutputTypeImage DownloadOutputType = "image"
	DownloadOutputTypeOther DownloadOutputType = "other"
)

var AllDownloadOutputTypes = []struct {
	Value  DownloadOutputType
	TSName string
}{
	{Value: DownloadOutputTypeVideo, TSName: "VIDEO"},
	{Value: DownloadOutputTypeAudio, TSName: "AUDIO"},
	{Value: DownloadOutputTypeImage, TSName: "IMAGE"},
	{Value: DownloadOutputTypeOther, TSName: "OTHER"},
}

type FileURL struct {
	URL      string            `json:"url"`
	Header   map[string]string `json:"header"`
	Language string            `json:"language,omitempty"` // 音轨或者字幕语言
}

type DownloadReq struct {
	FileURLs   []FileURL          `json:"fileURLs"`             // 待下载文件链接
	OutputType DownloadOutputType `json:"outputType"`           // 保存类型
	OutputDir  string             `json:"outputDir,omitempty"`  // 输出目录
	OutputName string             `json:"outputName,omitempty"` // 输出文件名，含文件后缀
}

type DownloadProgress struct {
	Total         int64 `json:"total"`         // 总大小
	Speed         int64 `json:"speed"`         // 速度
	RemainingTime int64 `json:"remainingTime"` // 剩余时间
}
