package types

type Preferences struct {
	User    PreferencesUser    `json:"user"`    // 用户相关设置
	General PreferencesGeneral `json:"general"` // 通用设置
	Network PreferencesNetwork `json:"network"` // 网络设置
	App     PreferencesApp     `json:"app"`     // 应用信息
	Server  PreferencesServer  `json:"server"`  // 服务器设置
}

type PreferencesUser struct {
	Username                string `json:"username"`                    // 用户名
	Email                   string `json:"email"`                       // 邮箱
	SessionID               string `json:"sessionID" yaml:"session-id"` // 登陆后的 token
	PreferredLanguageTag    string `json:"preferredLanguageTag"`        // 语言偏好
	ProfilePicture          string `json:"profilePicture"`              // 头像
	EmailVerified           bool   `json:"emailVerified"`               // 邮箱是否已验证
	AvailableTimes          int    `json:"availableTimes"`              // 可用次数
	CheckInDays             int    `json:"checkInDays"`                 // 签到天数
	LastCheckInAt           int64  `json:"lastCheckInAt"`               // 上次签到时间
	MembershipExpiresAt     *int64 `json:"membershipExpiresAt"`         // 会员过期时间
	TeamMembershipExpiresAt *int64 `json:"teamMembershipExpiresAt"`     // 团队会员过期时间
	TeamSize                int    `json:"teamSize"`                    // 团队规模
}

type PreferencesGeneral struct {
	UseSystemLanguage           bool   `json:"useSystemLanguage"`           // 使用系统语言
	Language                    string `json:"language"`                    // 语言
	DownloadDir                 string `json:"downloadDir"`                 // 下载目录
	AudioTrackDefaultLanguage   string `json:"audioTrackDefaultLanguage"`   // 音轨默认语言
	VideoDefaultResolution      string `json:"videoDefaultResolution"`      // 视频默认清晰度
	AuthorNameCreateDirectory   bool   `json:"authorNameCreateDirectory"`   // 按作者名称创建子文件夹
	PostClassifyCreateDirectory bool   `json:"postClassifyCreateDirectory"` // 按帖子分类创建子文件夹
	AddSerialNumber             bool   `json:"addSerialNumber"`             // 添加序号
	SaveAutoAlterMD5            bool   `json:"saveAutoAlterMD5"`            // 保存时自动修改 MD5
}

type NetworkRule string

const (
	NetworkRuleSystem NetworkRule = "system"
	NetworkRuleDirect NetworkRule = "direct"
	NetworkRuleHTTP   NetworkRule = "http"
	NetworkRuleSocks5 NetworkRule = "socks5"
)

var AllNetworkRules = []struct {
	Value  NetworkRule
	TSName string
}{
	{Value: NetworkRuleSystem, TSName: "SYSTEM"},
	{Value: NetworkRuleDirect, TSName: "DIRECT"},
	{Value: NetworkRuleHTTP, TSName: "HTTP"},
	{Value: NetworkRuleSocks5, TSName: "SOCKS5"},
}

type PreferencesNetwork struct {
	Enable    bool        `json:"enable"`             // 是否启用
	Rule      NetworkRule `json:"rule"`               // 代理类型: system, direct, http, socks5
	Host      string      `json:"host,omitempty"`     // 代理 host
	Port      string      `json:"port,omitempty"`     // 代理 port
	Username  string      `json:"username,omitempty"` // 代理用户名
	Password  string      `json:"password,omitempty"` // 代理密码
	TestSites []Site      `json:"testSites"`          // 测试网站列表
}

type Site struct {
	Name string `json:"name"` // 网站名称
	URL  string `json:"url"`  // 网站地址
}

type ExtractorTarget string

const (
	TargetLocal  ExtractorTarget = "local"  // 本地提取
	TargetServer ExtractorTarget = "server" // 远程提取
)

type Strategy struct {
	Name  string          `json:"name" yaml:"name"`
	Try   ExtractorTarget `json:"try" yaml:"try"`
	Catch ExtractorTarget `json:"catch" yaml:"catch"`
}

type RouteRules struct {
	Targets  []string `json:"targets" yaml:"targets"`
	Match    []string `json:"match" yaml:"match"`
	Strategy string   `json:"strategy" yaml:"strategy"`
}

type ExtractRule struct {
	Strategies      []Strategy   `json:"strategies" yaml:"strategies"`
	RouteRules      []RouteRules `json:"routeRules" yaml:"route-rules"`
	DefaultStrategy string       `json:"default-strategy" yaml:"default-strategy"`
}

type SupportedSite struct {
	Post     map[string][]string `json:"post"`
	Playlist map[string][]string `json:"playlist"`
}

type PreferencesApp struct {
	Name             string        `json:"name" yaml:"name"`                          // 软件名称
	AppVersion       string        `json:"appVersion" yaml:"app-version"`             // 当前软件版本
	ComponentVersion string        `json:"componentVersion" yaml:"component-version"` // 当前组件版本
	OfficialWebsite  string        `json:"officialWebsite" yaml:"official-website"`   // 官网
	SupportedSites   SupportedSite `json:"supportedSites" yaml:"supported-sites"`     // 支持提取的网站
	ExtractRule      ExtractRule   `json:"extractRule" yaml:"extract-rule"`           // 提取规则
}

type PreferencesServer struct {
	BaseApi string `json:"baseApi" yaml:"base-api"` // 服务器地址
}
