package types

type ConverterType string

const (
	ConverterTypeVideo ConverterType = "video"
	ConverterTypeAudio ConverterType = "audio"
	ConverterTypeImage ConverterType = "image"
)

var AllConverterTypes = []struct {
	Value  ConverterType
	TSName string
}{
	{Value: ConverterTypeVideo, TSName: "VIDEO"},
	{Value: ConverterTypeAudio, TSName: "AUDIO"},
	{Value: ConverterTypeImage, TSName: "IMAGE"},
}

type ConverterTask struct {
	Type          ConverterType `json:"type,omitempty"`
	InputFilePath []string      `json:"inputFilePath,omitempty"`
	OutputDir     string        `json:"outputDir,omitempty"`
	OutputName    string        `json:"outputName,omitempty"`
}

type ConverterProgress struct {
	Total         int64 `json:"total"`         // 总大小
	Speed         int64 `json:"speed"`         // 速度
	RemainingTime int64 `json:"remainingTime"` // 剩余时间
}
