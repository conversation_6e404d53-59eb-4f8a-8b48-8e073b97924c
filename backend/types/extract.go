package types

import "time"

type ExtractReq struct {
	URL    string `json:"url" binding:"required" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 网址
	Cursor string `json:"cursor" example:"123456789"`                                                   // 游标(用于播放列表的分页)
}

type Cursor struct {
	Index           int             `json:"index"`
	LastExtractor   ExtractorTarget `json:"lastExtractor"`
	ServerNextToken string          `json:"serverNextToken"`
}

// 单个帖子提取返回的数据
type PostResp struct {
	Text       string   `json:"text" example:"文案"`                          // 文案
	Medias     []*Media `json:"medias"`                                     // 媒体列表
	Overseas   int      `json:"overseas" example:"0"`                       // 是否海外资源 0: 否 1: 是
	ID         string   `json:"id,omitempty" example:"666888"`              // 帖子ID
	CreateTime string   `json:"create_time,omitempty" example:"3 days ago"` // 创建时间
}

type ExtractError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type Media struct {
	MediaType       string            `json:"media_type" example:"video"`                                                        // 媒体类型
	ResourceURL     string            `json:"resource_url" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"`                // 资源地址URL
	PreviewURL      string            `json:"preview_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"`       // 封面URL
	PreviewProxyURL string            `json:"preview_proxy_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 封面代理URL
	Formats         []Format          `json:"formats,omitempty"`                                                                 // 格式
	Headers         map[string]string `json:"headers,omitempty"`                                                                 // 请求头
}

type Format struct {
	Quality      int    `json:"quality,omitempty" example:"1080"`                                          // 质量
	VideoURL     string `json:"video_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 视频URL
	VideoExt     string `json:"video_ext,omitempty" example:"mp4"`                                         // 视频扩展名
	VideoSize    int    `json:"video_size,omitempty" example:"1024"`                                       // 视频大小
	AudioURL     string `json:"audio_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 音频URL
	AudioExt     string `json:"audio_ext,omitempty" example:"mp3"`                                         // 音频扩展名
	AudioSize    int    `json:"audio_size,omitempty" example:"1024"`                                       // 音频大小
	ImageURL     string `json:"image_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 图片URL
	Separate     int    `json:"separate" example:"0"`                                                      // 是否音视频分离 0: 否 1: 是
	QualityNote  string `json:"quality_note,omitempty" example:"4K/2K/1080P/720P/480P"`                    // 质量，如4K/2K/1080P/720P/480P
	AlternateURL string `json:"alternate_url,omitempty" example:"https://xxxx"`                            // 备用URL
	Language     string `json:"language,omitempty" example:"en"`                                           // 语言
}

// 播放列表提取返回的数据
type PlaylistResp struct {
	NextCursor string      `json:"next_cursor" example:"no_more"` // 游标
	HasMore    bool        `json:"has_more" example:"false"`      // 是否还有更多
	Posts      []*PostResp `json:"posts"`                         // 帖子列表
	Overseas   int         `json:"overseas" example:"0"`          // 是否海外资源 0: 否 1: 是
	User       *User       `json:"user,omitempty"`                // 用户信息
}

type User struct {
	Username string `json:"username" example:"jack"`                             // 用户名
	Avatar   string `json:"avatar" example:"https://www.example.com/avatar.png"` // 头像
}

// 字幕提取返回的数据
type SubtitlesResp struct {
	ID           string      `json:"id" example:"H14bBuluwB8"` // 帖子ID
	Text         string      `json:"text"`                     // 视频标题(文案)
	Description  string      `json:"description"`              // 描述(可能没有)
	Duration     *int        `json:"duration"`                 // 时长(秒)
	PublishedAt  *time.Time  `json:"published_at"`             // 发布时间
	ThumbnailURL string      `json:"thumbnail_url"`            // 缩略图(视频封面地址)  返回尺寸最大的图
	Subtitles    []*Subtitle `json:"subtitles"`                // 字幕列表
}

type Subtitle struct {
	LanguageName string         `json:"language_name"` // 语言名称
	LanguageCode string         `json:"language_code"` // 语言代码
	URLs         []*SubtitleURL `json:"urls"`          // 字幕URL
}

type SubtitleURL struct {
	URL    string `json:"url"`    // 字幕URL
	Format string `json:"format"` // 格式 按顺序如：srt、vtt、ttml、json3、srv1、srv2、srv3
}
