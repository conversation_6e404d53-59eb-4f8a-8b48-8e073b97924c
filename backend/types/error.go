package types

import "encoding/json"

type JSError struct {
	Message    string `json:"message"`
	HttpCode   int    `json:"httpCode"`   // rest http 状态码
	ServerCode string `json:"serverCode"` // 服务端定义的错误码
	ClientCode string `json:"clientCode"` // 客户端定义的错误码
	Detail     any    `json:"detail,omitempty"`
	cause      error
}

func (e *JSError) Error() string {
	data, _ := json.Marshal(e)
	return string(data)
}

func (e *JSError) Wrap(err error) *JSError {
	if err == nil {
		return e
	}
	return &JSError{
		Message:    e.Message,
		HttpCode:   e.HttpCode,
		ServerCode: e.ServerCode,
		Detail:     e.Detail,
		ClientCode: e.ClientCode,
		cause:      err,
	}
}

func (e *JSError) Unwrap() error {
	return e.cause
}
