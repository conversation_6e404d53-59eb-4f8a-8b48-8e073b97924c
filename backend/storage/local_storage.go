package storage

import (
	"github.com/apparentlymart/go-userdirs/userdirs"
	"henghengman-desktop/backend/constant"
	"os"
	"path"
)

type LocalStorage struct {
	confPath string
}

func NewLocalStore(filename string) *LocalStorage {
	dirs := userdirs.ForApp(constant.PackageName, constant.PackageName, constant.PackageName)
	return &LocalStorage{
		confPath: path.Join(dirs.ConfigHome(), filename),
	}
}

func (l *LocalStorage) Load() ([]byte, error) {
	d, err := os.ReadFile(l.confPath)
	if err != nil {
		return nil, err
	}
	return d, err
}

func (l *LocalStorage) Store(data []byte) error {
	dir := path.Dir(l.confPath)
	if err := ensureDirExists(dir); err != nil {
		return err
	}
	if err := os.WriteFile(l.confPath, data, 0777); err != nil {
		return err
	}
	return nil
}

func ensureDirExists(path string) error {
	_, err := os.Stat(path)
	if os.IsNotExist(err) {
		if err = os.Mkdir(path, 0777); err != nil {
			return err
		}
	}
	return nil
}
