server:
  base-api: "https://api.aipark.top"
app:
  name: "哼哼猫"
  version: "0.0.1"
  component-version: "0.0.1"
  official-website: https://aipark.top
  supported_sites:
    post:
      en:
        - YouTube
        - Instagram
        - Twitter
        - Tiktok
      zh:
        - 哔哩哔哩
        - 快手
        - Youtube
        - Instagram
    playlist:
      en:
        - YouTube (Channel / Shorts / Playlist)
        - Instagram (Profile / Hashtag Feed)
        - Twitter
        - Bilibili (Profile / Collection)
        - Ac<PERSON>un
        - <PERSON><PERSON><PERSON> (Profile / Hashtag Feed)
      zh:
        - YouTube(频道页 / Shorts / 播放列表)
        - Instagram(作者主页 / Hashtag列表)
        - Twitter
        - 哔哩哔哩(主页 / 合集)
  extract-rule:
    default-strategy: clientFirst
    strategies:
      - name: "serverOnly" # 仅服务端
        try: server
      - name: "clientOnly" # 仅客户端
        try: local
      - name: "serverFirst" # 服务端优先
        try: server
        catch: local
      - name: "clientFirst" # 客户端优先
        try: local
        catch: server
    route-rules: # 从上到下匹配
      - strategy: 'clientOnly'
        match:
          - 'youtube.com'
        targets:
          - "post"
          - "playlist"
      - strategy: 'serverOnly'
        match:
          - 'xiaohongshu.com'
        targets:
          - "post"
          - "playlist"