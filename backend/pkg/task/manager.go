package task

import (
	"context"
	"sync"
)

type RegisterTask func(task *TaskContext)

type TaskContext struct {
	ID      string
	Context context.Context
	Cancel  context.CancelFunc
}

type Manager struct {
	pool sync.Map // taskID -> Task
}

func NewManager() *Manager {
	return &Manager{
		pool: sync.Map{},
	}
}

// Register 注册一个新任务
func (m *Manager) Register(task *TaskContext) {
	m.pool.Store(task.ID, task)
}

// Cancel 取消指定任务
func (m *Manager) Cancel(taskID string) bool {
	if value, ok := m.pool.Load(taskID); ok {
		if task, ok := value.(*TaskContext); ok {
			task.Cancel()
			m.pool.Delete(taskID)
			return true
		}
	}
	return false
}

// Get 获取指定任务
func (m *Manager) Get(taskID string) (*TaskContext, bool) {
	if value, ok := m.pool.Load(taskID); ok {
		if task, ok := value.(*TaskContext); ok {
			return task, true
		}
	}
	return nil, false
}

// CancelAll 取消所有任务
func (m *Manager) CancelAll() {
	m.pool.Range(func(key, value interface{}) bool {
		if task, ok := value.(*TaskContext); ok {
			task.Cancel()
		}
		m.pool.Delete(key)
		return true
	})
}
