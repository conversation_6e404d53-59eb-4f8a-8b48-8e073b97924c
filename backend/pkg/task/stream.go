package task

import (
	"context"

	"github.com/google/uuid"
)

type Resolve[T any] func(taskID string, data T)
type Reject func(taskID string, err error)
type Executor[T any] func(ctx context.Context, taskID string, resolve Resolve[T], reject Reject) error

type StreamPromise[T any] struct {
	taskCtx    *TaskContext
	taskID     string
	resolve    Resolve[T]
	reject     Reject
	finalize   func(ctx context.Context, taskID string)
	executor   Executor[T]
	registerFn RegisterTask
}

func NewStreamPromise[T any](ctx context.Context, executor Executor[T]) *StreamPromise[T] {
	ctx, cancel := context.WithCancel(ctx)
	task := &TaskContext{
		Context: ctx,
		Cancel:  cancel,
	}
	return &StreamPromise[T]{
		taskCtx:  task,
		executor: executor,
	}
}

func (s *StreamPromise[T]) Then(fn Resolve[T]) *StreamPromise[T] {
	s.resolve = fn
	return s
}

func (s *StreamPromise[T]) Catch(fn Reject) *StreamPromise[T] {
	s.reject = fn
	return s
}

func (s *StreamPromise[T]) Finally(fn func(ctx context.Context, taskID string)) *StreamPromise[T] {
	s.finalize = fn
	return s
}

func (s *StreamPromise[T]) Start() string {
	s.taskID = uuid.NewString()
	s.taskCtx.ID = s.taskID
	s.registerFn(s.taskCtx)
	go func() {
		if err := s.executor(s.taskCtx.Context, s.taskID, s.resolve, s.reject); err != nil {
			s.reject(s.taskID, err)
		}
		if s.finalize != nil {
			s.finalize(s.taskCtx.Context, s.taskID)
		}
	}()
	return s.taskID
}

func (s *StreamPromise[T]) OnRegister(register RegisterTask) *StreamPromise[T] {
	s.registerFn = register
	return s
}
