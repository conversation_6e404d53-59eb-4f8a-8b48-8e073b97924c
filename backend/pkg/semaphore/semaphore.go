// Package semaphore 动态信号量
package semaphore

import (
	"context"
	"sync"
)

// Semaphore 动态可调整的信号量
type Semaphore struct {
	mu       sync.RWMutex
	permits  chan struct{}
	capacity int
	acquired int
}

// NewSemaphore 创建动态信号量
func NewSemaphore(capacity int) *Semaphore {
	sem := &Semaphore{
		permits:  make(chan struct{}, capacity),
		capacity: capacity,
		acquired: 0,
	}

	// 初始化信号量
	for i := 0; i < capacity; i++ {
		sem.permits <- struct{}{}
	}

	return sem
}

// Acquire 获取信号量
func (s *Semaphore) Acquire(ctx context.Context) error {
	// 先判断上下文是否已经取消了，因为select是随机选择case
	if ctx.Err() != nil {
		return ctx.Err()
	}
	select {
	case <-s.permits:
		s.mu.Lock()
		s.acquired++
		s.mu.Unlock()
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// Release 释放信号量
func (s *Semaphore) Release() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.acquired > 0 {
		s.acquired--
		// 只有当释放后的acquired数量小于容量时，才放回permit
		// 这样可以处理超容量的情况
		if s.acquired < s.capacity {
			select {
			case s.permits <- struct{}{}:
			default:
				// 如果 channel 已满就忽略，这是正常的
			}
		}
	}
}

// SetCapacity 动态调整信号量容量
func (s *Semaphore) SetCapacity(newCapacity int) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if newCapacity == s.capacity {
		return
	}

	// 创建新的 channel
	newPermits := make(chan struct{}, newCapacity)

	// 计算应该有多少个可用的 permits
	shouldHavePermits := newCapacity - s.acquired
	if shouldHavePermits < 0 {
		shouldHavePermits = 0
	}

	// 向新 channel 中放入正确数量的 permits
	for i := 0; i < shouldHavePermits; i++ {
		newPermits <- struct{}{}
	}

	// 更新容量和 channel
	s.capacity = newCapacity
	s.permits = newPermits
}

// GetStats 获取信号量统计信息
func (s *Semaphore) GetStats() (capacity, acquired, available int) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.capacity, s.acquired, len(s.permits)
}
