package utils

import (
	"fmt"
	"henghengman-desktop/backend/types"
)

func GetProxy(network types.PreferencesNetwork) string {
	if !network.Enable {
		return "system"
	}
	auth := ""
	if network.Username != "" && network.Password != "" {
		auth = fmt.Sprintf("%s:%s@", network.Username, network.Password)
	}
	switch network.Rule {
	case types.NetworkRuleSystem:
		return "system"
	case types.NetworkRuleHTTP:
		return fmt.Sprintf("http://%s%s:%s", auth, network.Host, network.Port)
	case types.NetworkRuleSocks5:
		return fmt.Sprintf("socks5://%s%s:%s", auth, network.Host, network.Port)
	case types.NetworkRuleDirect:
		return "direct"
	default:
		return "system"
	}
}
