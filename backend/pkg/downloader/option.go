package downloader

import (
	"henghengman-desktop/backend/pkg/progress"
	"net/http"
)

type options struct {
	proxy   string
	header  http.Header
	monitor progress.Monitor
}

func newOption(opts ...Option) *options {
	opt := &options{}
	for _, o := range opts {
		o(opt)
	}
	return opt
}

type Option func(*options)

func WithProxy(proxy string) Option {
	return func(d *options) {
		d.proxy = proxy
	}
}

func WithHeader(header http.Header) Option {
	return func(d *options) {
		d.header = header
	}
}

func WithMonitor(monitor progress.Monitor) Option {
	return func(d *options) {
		d.monitor = monitor
	}
}
