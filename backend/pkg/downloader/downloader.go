package downloader

import (
	"context"
	"henghengman-desktop/backend/pkg/progress"
	"io"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"os"
	"path"
	"strings"
	"time"

	"github.com/mattn/go-ieproxy"
	"github.com/spf13/cast"
)

const downloadingFileSuffix = ".downloading"

func initClient(proxy string) *http.Client {
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}
	switch proxy {
	case "direct", "":
	case "system":
		transport.Proxy = ieproxy.GetProxyFunc()
	default:
		proxyURL, err := url.Parse(proxy)
		if err != nil {
			slog.Warn("parse proxy url failed", "err", err)
		} else {
			transport.Proxy = http.ProxyURL(proxyURL)
		}
	}
	return &http.Client{
		Transport: transport,
	}
}

type Client struct {
}

func NewClient() *Client {
	return &Client{}
}

func (c *Client) Download(ctx context.Context, downloadURL, outputDir, outputName string, opts ...Option) error {
	// todo: 断点续传
	opt := newOption(opts...)
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, downloadURL, nil)
	req.Header = opt.header
	resp, err := initClient(opt.proxy).Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	outputExt := path.Ext(outputName)
	outputName = strings.TrimSuffix(outputName, outputExt)

	downloadingFilePath := path.Join(outputDir, outputName+downloadingFileSuffix)
	f, err := os.OpenFile(downloadingFilePath, os.O_CREATE|os.O_WRONLY, os.ModePerm)
	if err != nil {
		return err
	}
	defer f.Close()
	var reader io.Reader = resp.Body
	if opt.monitor != nil {
		tp := opt.monitor.(progress.TotalProgress)
		tp.IncTotal(cast.ToInt64(resp.Header.Get("Content-Length")))
		reader = progress.NewReader(resp.Body, opt.monitor.Report)
	}
	_, err = io.Copy(f, reader)
	if err != nil {
		return err
	}
	outputFilePath := path.Join(outputDir, outputName+outputExt)
	err = os.Rename(downloadingFilePath, outputFilePath)
	if err != nil {
		return err
	}
	return nil
}
