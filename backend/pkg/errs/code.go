package errs

type ClientCode string

const (
	ClientCodeNetworkError               ClientCode = "networkError"
	ClientCodeSerializeError             ClientCode = "jsonParseError"
	ClientCodeGetFileInfoError           ClientCode = "fileGetInfoError"
	ClientCodeReadFileError              ClientCode = "fileReadFileError"
	ClientCodeWriteFileError             ClientCode = "fileWriteFileError"
	ClientCodeFileNotFoundError          ClientCode = "fileNotFoundError"
	ClientCodeExecError                  ClientCode = "execError"
	ClientCodeUnsupportedConvertType     ClientCode = "unsupportedConvertType"
	ClientCodeConvertError               ClientCode = "convertError"
	ClientCodeNoAudioError               ClientCode = "noAudioError"
	ClientCodeNoVideoError               ClientCode = "noVideoError"
	ClientCodeConvertFormatNotFoundError ClientCode = "convertFormatNotFoundError"
	ClientCodeFFProbeError               ClientCode = "ffprobeError"
	ClientCodeInvalidParameterError      ClientCode = "invalidParameterError"
	ClientCodeGetMD5Error                ClientCode = "getMD5Error"
	ClientCodeDiskFullError              ClientCode = "diskFullError"
	ClientCodeDiskPermissionError        ClientCode = "diskPermissionError"
	ClientCodePackageNotFoundError       ClientCode = "packageNotFoundError"
	ClientCodeComponentInfoError         ClientCode = "componentInfoError"
	ClientCodeUnsupportedOSError         ClientCode = "unsupportedOSError"
	ClientCodeInstallError               ClientCode = "installError"
	ClientCodeFileLockTimeoutError       ClientCode = "fileLockTimeoutError"
	ClientCodeFileLockCheckError         ClientCode = "fileLockCheckError"
	ClientCodeDownloadRetryExceededError ClientCode = "downloadRetryExceededError"
	ClientCodeFileLockError              ClientCode = "fileLockError"
)

func (code ClientCode) String() string {
	return string(code)
}
