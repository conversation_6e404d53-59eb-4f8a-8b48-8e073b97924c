package errs

import (
	"encoding/json"
	"errors"
	"henghengman-desktop/backend/types"
	"io"
)

func ParseServerRespError(code int, read io.Reader) *types.JSError {
	var jsErr types.JSError
	var errorResp struct {
		Message string `json:"message"`
		Code    string `json:"code,omitempty"`
		Detail  any    `json:"detail,omitempty"`
	}
	if err := json.NewDecoder(read).Decode(&errorResp); err != nil {
		return NewSerializeError().Wrap(err)
	}
	jsErr.HttpCode = code
	jsErr.ServerCode = errorResp.Code
	jsErr.Message = errorResp.Message
	jsErr.Detail = errorResp.Detail
	return &jsErr
}

func NewServerError(httpCode int, serverCode string) *types.JSError {
	return &types.JSError{
		HttpCode:   httpCode,
		ServerCode: serverCode,
	}
}

func NewClientError(code ClientCode) *types.JSError {
	return &types.JSError{
		ClientCode: code.String(),
	}
}

// NewNetworkError 网络错误
func NewNetworkError() *types.JSError {
	return NewClientError(ClientCodeNetworkError)
}

// NewSerializeError json解析错误
func NewSerializeError() *types.JSError {
	return NewClientError(ClientCodeSerializeError)
}

// NewGetFileInfoError 获取文件信息错误
func NewGetFileInfoError() *types.JSError {
	return NewClientError(ClientCodeGetFileInfoError)
}

// NewReadFileError 读取文件错误
func NewReadFileError() *types.JSError {
	return NewClientError(ClientCodeReadFileError)
}

// NewWriteFileError 写入文件错误
func NewWriteFileError() *types.JSError {
	return NewClientError(ClientCodeWriteFileError)
}

// NewFileNotFoundError 文件不存在错误
func NewFileNotFoundError(path string) *types.JSError {
	return &types.JSError{
		ClientCode: ClientCodeFileNotFoundError.String(),
		Detail:     path,
	}
}

// NewExecError 执行命令失败
func NewExecError() *types.JSError {
	return NewClientError(ClientCodeExecError)
}

// NewPaymentRequiredError 402
func NewPaymentRequiredError() *types.JSError {
	return NewServerError(402, "")
}

// NewUnsupportedConvertTypeError 不支持的转换类型
func NewUnsupportedConvertTypeError() *types.JSError {
	return NewClientError(ClientCodeUnsupportedConvertType)
}

// NewConvertError 转换错误
func NewConvertError() *types.JSError {
	return NewClientError(ClientCodeConvertError)
}

// NewNoAudioError 没有音频
func NewNoAudioError() *types.JSError {
	return NewClientError(ClientCodeNoAudioError)
}

// NewNoVideoError 没有视频
func NewNoVideoError() *types.JSError {
	return NewClientError(ClientCodeNoVideoError)
}

// NewConvertFormatNotFoundError 转换格式不存在
func NewConvertFormatNotFoundError() *types.JSError {
	return NewClientError(ClientCodeConvertFormatNotFoundError)
}

// NewFFProbeError ffprobe错误
func NewFFProbeError() *types.JSError {
	return NewClientError(ClientCodeFFProbeError)
}

// NewInvalidParameterError 无效参数
func NewInvalidParameterError(field string) *types.JSError {
	return &types.JSError{
		ClientCode: ClientCodeInvalidParameterError.String(),
		Detail:     field,
	}
}

// NewGetMD5Error 获取md5失败
func NewGetMD5Error() *types.JSError {
	return NewClientError(ClientCodeGetMD5Error)
}

// NewDiskFullError 磁盘空间不足
func NewDiskFullError() *types.JSError {
	return NewClientError(ClientCodeDiskFullError)
}

// NewDiskPermissionError 磁盘权限错误
func NewDiskPermissionError() *types.JSError {
	return NewClientError(ClientCodeDiskPermissionError)
}

// NewPackageNotFoundError 软件包不存在
func NewPackageNotFoundError() *types.JSError {
	return NewClientError(ClientCodePackageNotFoundError)
}

// NewComponentInfoError 获取组件信息失败
func NewComponentInfoError() *types.JSError {
	return NewClientError(ClientCodeComponentInfoError)
}

// NewUnsupportedOSError 不支持的操作系统
func NewUnsupportedOSError(os string) *types.JSError {
	return &types.JSError{
		ClientCode: ClientCodeUnsupportedOSError.String(),
		Detail:     os,
	}
}

// NewInstallError 安装失败
func NewInstallError() *types.JSError {
	return NewClientError(ClientCodeInstallError)
}

// NewFileLockTimeoutError 文件锁定超时
func NewFileLockTimeoutError() *types.JSError {
	return NewClientError(ClientCodeFileLockTimeoutError)
}

// NewFileLockCheckError 检查文件锁定状态失败
func NewFileLockCheckError() *types.JSError {
	return NewClientError(ClientCodeFileLockCheckError)
}

// NewDownloadRetryExceededError 下载重试次数超限
func NewDownloadRetryExceededError(retries int) *types.JSError {
	return &types.JSError{
		ClientCode: ClientCodeDownloadRetryExceededError.String(),
		Detail:     retries,
	}
}

// NewFileLockError 文件被锁定
func NewFileLockError(path string) *types.JSError {
	return &types.JSError{
		ClientCode: ClientCodeFileLockError.String(),
		Detail:     path,
	}
}

// IsFileLockError 检查是否是文件锁定错误
func IsFileLockError(err error) bool {
	var jsErr *types.JSError
	if errors.As(err, &jsErr) {
		return jsErr.ClientCode == ClientCodeFileLockError.String()
	}
	return false
}
