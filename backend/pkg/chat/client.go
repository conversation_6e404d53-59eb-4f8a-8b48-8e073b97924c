package chat

import (
	"bytes"
	"context"
	"encoding/json"
	"henghengman-desktop/backend/pkg/env"
	"henghengman-desktop/backend/pkg/errs"
	"henghengman-desktop/backend/pkg/utils"
	"henghengman-desktop/backend/types"
	"log/slog"
	"net/http"
	"time"
)

const (
	extractPostPath        = "/extract/post"
	extractPlayListPath    = "/extract/playlist"
	extractEligibilityPath = "/extract/eligibility"
	extractBillingPath     = "/extract/billing"
	getYtDlpInfoURL        = "https://api.snapany.com/desktop/ytdlp"
)

type Client struct {
	httpClient *http.Client
	baseApi    string
}

func NewClient(baseApi string) *Client {
	return &Client{
		httpClient: http.DefaultClient,
		baseApi:    baseApi,
	}
}

func (c *Client) header(opt *option) http.Header {
	header := http.Header{}
	header.Add("accept-language", env.GetLanguage())
	header.Add("g-timezone", utils.GetTimezone())
	if opt.sessionID != "" {
		header.Add("g-session-id", opt.sessionID)
	}
	return header
}

func (c *Client) IsMembership(user types.PreferencesUser) bool {
	if user.MembershipExpiresAt == nil {
		return false
	}
	return *user.MembershipExpiresAt > time.Now().Unix()
}

func (c *Client) ExtractEligibility(ctx context.Context, opts ...Option) (bool, error) {
	opt := newOption(opts...)
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, c.baseApi+extractEligibilityPath, nil)
	req.Header = c.header(opt)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false, errs.NewNetworkError().Wrap(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return false, errs.ParseServerRespError(resp.StatusCode, resp.Body)
	}
	var data struct {
		Eligible bool `json:"eligible"`
	}
	err = json.NewDecoder(resp.Body).Decode(&data)
	if err != nil {
		return false, errs.NewSerializeError().Wrap(err)
	}
	return data.Eligible, nil
}

func (c *Client) ExtractBilling(ctx context.Context, opts ...Option) error {
	opt := newOption(opts...)
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, c.baseApi+extractBillingPath, nil)
	req.Header = c.header(opt)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return errs.NewNetworkError().Wrap(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return errs.ParseServerRespError(resp.StatusCode, resp.Body)
	}
	return nil
}

func (c *Client) Post(ctx context.Context, url string, opts ...Option) (*types.PostResp, error) {
	opt := newOption(opts...)
	if opt.sem != nil {
		if err := opt.sem.Acquire(ctx); err != nil {
			return nil, err
		}
		defer opt.sem.Release()
	}

	reqData := map[string]string{
		"url": url,
	}
	reqBody, _ := json.Marshal(reqData)
	req, _ := http.NewRequestWithContext(ctx, http.MethodPost, c.baseApi+extractPostPath, bytes.NewReader(reqBody))
	req.Header = c.header(opt)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		slog.ErrorContext(ctx, "提取帖子", "error", err)
		return nil, errs.NewNetworkError().Wrap(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, errs.ParseServerRespError(resp.StatusCode, resp.Body)
	}
	var data types.PostResp
	err = json.NewDecoder(resp.Body).Decode(&data)
	if err != nil {
		return nil, errs.NewSerializeError().Wrap(err)
	}
	return &data, nil
}

func (c *Client) PlayList(ctx context.Context, url string, opts ...Option) (*types.PlaylistResp, error) {
	opt := newOption(opts...)
	if opt.sem != nil {
		if err := opt.sem.Acquire(ctx); err != nil {
			return nil, err
		}
		defer opt.sem.Release()
	}
	reqData := map[string]string{
		"url": url,
	}
	reqBody, _ := json.Marshal(reqData)
	req, _ := http.NewRequestWithContext(ctx, http.MethodPost, c.baseApi+extractPlayListPath, bytes.NewReader(reqBody))
	req.Header = c.header(opt)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		slog.ErrorContext(ctx, "提取播放列表", "error", err)
		return nil, errs.NewNetworkError().Wrap(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, errs.ParseServerRespError(resp.StatusCode, resp.Body)
	}
	var data types.PlaylistResp
	err = json.NewDecoder(resp.Body).Decode(&data)
	if err != nil {
		return nil, errs.NewSerializeError().Wrap(err)
	}
	return &data, nil
}

type GetYtDlpInfoResp struct {
	DownloadUrls struct {
		MacOS   string `json:"macOS"`
		Windows string `json:"windows"`
	} `json:"downloadUrls"`
	Version string `json:"version"`
}

func (c *Client) GetYtDlpInfo(ctx context.Context) (*GetYtDlpInfoResp, error) {
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, getYtDlpInfoURL, nil)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		slog.ErrorContext(ctx, "获取yt-dlp信息", "error", err)
		return nil, errs.NewNetworkError().Wrap(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, errs.ParseServerRespError(resp.StatusCode, resp.Body)
	}
	var data GetYtDlpInfoResp
	err = json.NewDecoder(resp.Body).Decode(&data)
	if err != nil {
		return nil, errs.NewSerializeError().Wrap(err)
	}
	return &data, nil
}
