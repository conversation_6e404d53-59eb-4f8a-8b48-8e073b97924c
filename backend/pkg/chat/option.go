package chat

import "henghengman-desktop/backend/pkg/semaphore"

type Option func(*option)

type option struct {
	sem       *semaphore.Semaphore
	sessionID string
}

func WithSemaphore(sem *semaphore.Semaphore) Option {
	return func(o *option) {
		o.sem = sem
	}
}

func WithSessionID(sessionID string) Option {
	return func(o *option) {
		o.sessionID = sessionID
	}
}

func newOption(opts ...Option) *option {
	o := &option{}
	for _, opt := range opts {
		opt(o)
	}
	return o
}
