package selector

import (
	"henghengman-desktop/backend/types"
	"net/url"
	"slices"
)

func getStrategy(extractRule types.ExtractRule, name string) types.Strategy {
	for _, item := range extractRule.Strategies {
		if item.Name == name {
			return item
		}
	}
	panic("未定义的提取策略")
}

func SelectStrategy(extractRule types.ExtractRule, targetURL string, target string) types.Strategy {
	u, err := url.Parse(targetURL)
	if err != nil {
		return getStrategy(extractRule, extractRule.DefaultStrategy)
	}
	strategyName := ""
	for _, routeRule := range extractRule.RouteRules {
		if slices.Contains(routeRule.Targets, target) && slices.Contains(routeRule.Match, u.Host) {
			strategyName = routeRule.Strategy
			break
		}
	}
	if strategyName == "" {
		return getStrategy(extractRule, extractRule.DefaultStrategy)
	}
	return getStrategy(extractRule, strategyName)
}
