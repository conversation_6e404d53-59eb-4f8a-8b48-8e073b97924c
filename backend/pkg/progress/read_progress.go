package progress

import (
	"io"
)

// Reader 读取进度包装
type Reader struct {
	reader io.Reader
	report func(done int64)
}

func NewReader(reader io.Reader, report func(done int64)) io.Reader {
	return &Reader{
		reader: reader,
		report: report,
	}
}

func (s *Reader) Read(buf []byte) (n int, err error) {
	n, err = s.reader.Read(buf)
	if n > 0 {
		if s.report != nil {
			s.report(int64(n))
		}
	}
	return n, err
}
