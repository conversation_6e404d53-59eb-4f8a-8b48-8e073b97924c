package progress

import (
	"sync"
	"sync/atomic"
	"time"
)

type aggregatorOptions struct {
	intervalTime time.Duration
}

type AggregatorOption func(o *aggregatorOptions)

func WithIntervalTime(intervalTime time.Duration) AggregatorOption {
	return func(o *aggregatorOptions) {
		o.intervalTime = intervalTime
	}
}

var _ Monitor = (*Aggregator)(nil)

type Aggregator struct {
	done         atomic.Int64
	total        atomic.Int64
	one          sync.Once
	stop         chan struct{}
	callback     ReportCallback
	lastTimeDone int64
	opts         aggregatorOptions
}

func NewAggregator(total int64, callback ReportCallback, opts ...AggregatorOption) Monitor {
	o := aggregatorOptions{
		intervalTime: time.Second,
	}
	for _, opt := range opts {
		opt(&o)
	}
	totalAtomic := atomic.Int64{}
	totalAtomic.Store(total)
	return &Aggregator{
		stop:     make(chan struct{}),
		callback: callback,
		total:    totalAtomic,
		opts:     o,
	}
}

func (g *Aggregator) Start() {}

func (g *Aggregator) Report(done int64) {
	g.one.Do(func() {
		go func() {
			ticker := time.NewTicker(g.opts.intervalTime)
			defer ticker.Stop()
			for {
				select {
				case <-g.stop:
					ticker.Stop()
					//g.callback(g._type, g.total, g.done.Load()) // 最后一次上报
					return
				case <-ticker.C:
					currentDone := g.done.Load()
					total := g.total.Load()
					speed := max(currentDone-g.lastTimeDone, 0)

					// 计算剩余时间
					var remainingTime int64 = 0
					if speed > 0 && currentDone < total {
						remainingTime = (total - currentDone) / speed
					}

					g.lastTimeDone = currentDone
					g.callback(total, g.done.Load(), speed, remainingTime)
				}
			}
		}()
	})
	g.done.Add(done)
}

func (g *Aggregator) Stop() {
	select { // 防止重复调用stop方法panic
	case <-g.stop:
	default:
		close(g.stop)
	}
}

func (g *Aggregator) SetTotal(total int64) {
	g.total.Store(total)
}

func (g *Aggregator) IncTotal(n int64) {
	g.total.Add(n)
}
