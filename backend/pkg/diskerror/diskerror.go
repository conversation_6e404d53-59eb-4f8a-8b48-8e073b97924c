package diskerror

import (
	"errors"
	"os"
	"runtime"
	"syscall"
)

// IsDiskFull 判断错误是否是“磁盘空间不足”
func IsDiskFull(err error) bool {
	if err == nil {
		return false
	}

	// Linux/macOS: ENOSPC
	if errors.Is(err, syscall.ENOSPC) {
		return true
	}

	// Windows: ERROR_DISK_FULL (112)
	const ERROR_DISK_FULL = 0x70 // 112

	// Windows: ERROR_HANDLE_DISK_FULL (39)
	const ERROR_HANDLE_DISK_FULL = 0x27 // 39

	// 可能被 PathError/SyscallError 包裹
	var pathErr *os.PathError
	if errors.As(err, &pathErr) {
		if errno, ok := pathErr.Err.(syscall.Errno); ok {
			if runtime.GOOS == "windows" && (errno == ERROR_DISK_FULL || errno == ERROR_HANDLE_DISK_FULL) {
				return true
			}
		}
	}

	var syscallErr *os.SyscallError
	if errors.As(err, &syscallErr) {
		if errno, ok := syscallErr.Err.(syscall.Errno); ok {
			if runtime.GOOS == "windows" && (errno == ERROR_DISK_FULL || errno == ERROR_HANDLE_DISK_FULL) {
				return true
			}
		}
	}

	// 也可能是裸 Errno
	if errno, ok := err.(syscall.Errno); ok {
		if runtime.GOOS == "windows" && (errno == ERROR_DISK_FULL || errno == ERROR_HANDLE_DISK_FULL) {
			return true
		}
	}

	return false
}

func IsPermission(err error) bool {
	for {
		switch x := err.(type) {
		case interface{ Unwrap() error }:
			err = x.Unwrap()
			if err == nil {
				return false
			}
		case interface{ Unwrap() []error }:
			for _, err := range x.Unwrap() {
				if IsPermission(err) {
					return true
				}
			}
			return false
		default:
			return os.IsPermission(err)
		}
	}
}
