package converter

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"os/exec"
)

type Disposition struct {
	Default         int `json:"default"`
	Dub             int `json:"dub"`
	Original        int `json:"original"`
	Comment         int `json:"comment"`
	Lyrics          int `json:"lyrics"`
	Karaoke         int `json:"karaoke"`
	Forced          int `json:"forced"`
	HearingImpaired int `json:"hearing_impaired"`
	VisualImpaired  int `json:"visual_impaired"`
	CleanEffects    int `json:"clean_effects"`
	AttachedPic     int `json:"attached_pic"`
	TimedThumbnails int `json:"timed_thumbnails"`
	NonDiegetic     int `json:"non_diegetic"`
	Captions        int `json:"captions"`
	Descriptions    int `json:"descriptions"`
	Metadata        int `json:"metadata"`
	Dependent       int `json:"dependent"`
	StillImage      int `json:"still_image"`
	Multilayer      int `json:"multilayer"`
}

type StreamTags struct {
	Language    string `json:"language"`
	HandlerName string `json:"handler_name"`
	VendorId    string `json:"vendor_id"`
}

type Stream struct {
	Index              int         `json:"index"`
	CodecName          string      `json:"codec_name"`
	CodecLongName      string      `json:"codec_long_name"`
	Profile            string      `json:"profile"`
	CodecType          string      `json:"codec_type"`
	CodecTagString     string      `json:"codec_tag_string"`
	CodecTag           string      `json:"codec_tag"`
	Width              int         `json:"width"`
	Height             int         `json:"height"`
	CodedWidth         int         `json:"coded_width"`
	CodedHeight        int         `json:"coded_height"`
	ClosedCaptions     int         `json:"closed_captions"`
	FilmGrain          int         `json:"film_grain"`
	HasBFrames         int         `json:"has_b_frames"`
	SampleAspectRatio  string      `json:"sample_aspect_ratio"`
	DisplayAspectRatio string      `json:"display_aspect_ratio"`
	PixFmt             string      `json:"pix_fmt"`
	Level              int         `json:"level"`
	ColorRange         string      `json:"color_range"`
	ColorSpace         string      `json:"color_space"`
	ColorTransfer      string      `json:"color_transfer"`
	ColorPrimaries     string      `json:"color_primaries"`
	ChromaLocation     string      `json:"chroma_location"`
	FieldOrder         string      `json:"field_order"`
	Refs               int         `json:"refs"`
	ViewIdsAvailable   string      `json:"view_ids_available"`
	ViewPosAvailable   string      `json:"view_pos_available"`
	Id                 string      `json:"id"`
	RFrameRate         string      `json:"r_frame_rate"`
	AvgFrameRate       string      `json:"avg_frame_rate"`
	TimeBase           string      `json:"time_base"`
	StartPts           int         `json:"start_pts"`
	StartTime          string      `json:"start_time"`
	DurationTs         int         `json:"duration_ts"`
	Duration           string      `json:"duration"`
	BitRate            string      `json:"bit_rate"`
	ExtradataSize      int         `json:"extradata_size"`
	Disposition        Disposition `json:"disposition"`
	Tags               StreamTags  `json:"tags"`
	Format             Format      `json:"-"`
}

type FormatTags struct {
	MinorVersion     string `json:"minor_version"`
	MajorBrand       string `json:"major_brand"`
	CompatibleBrands string `json:"compatible_brands"`
	Comment          string `json:"comment"`
	Encoder          string `json:"encoder"`
}

type Format struct {
	Filename       string     `json:"filename"`
	NbStreams      int        `json:"nb_streams"`
	NbPrograms     int        `json:"nb_programs"`
	NbStreamGroups int        `json:"nb_stream_groups"`
	FormatName     string     `json:"format_name"`
	FormatLongName string     `json:"format_long_name"`
	StartTime      string     `json:"start_time"`
	Duration       string     `json:"duration"`
	Size           string     `json:"size"`
	BitRate        string     `json:"bit_rate"`
	ProbeScore     int        `json:"probe_score"`
	Tags           FormatTags `json:"tags"`
}

type FFProbeRes struct {
	Streams []Stream `json:"streams"`
	Format  *Format  `json:"format"`
}

func GetFFProbeRes(ctx context.Context, filePath string) (*FFProbeRes, error) {
	cmd := exec.CommandContext(ctx, GetFFProbeBinPath(ctx), "-v", "quiet", "-print_format", "json", "-show_format", "-show_streams", filePath)
	slog.DebugContext(ctx, "获取流信息", "cmd", cmd.String())
	resp, err := cmd.Output()
	if resp == nil && err != nil {
		return nil, fmt.Errorf("run ffprobe: %w", err)
	}
	var ffprobeRes FFProbeRes
	err = json.Unmarshal(resp, &ffprobeRes)
	if err != nil {
		return nil, fmt.Errorf("unmarshal ffprobe response: %w", err)
	}
	for i := range ffprobeRes.Streams {
		ffprobeRes.Streams[i].Format = *ffprobeRes.Format
	}
	return &ffprobeRes, nil
}
