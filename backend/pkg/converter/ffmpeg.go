package converter

import (
	"bufio"
	"context"
	"errors"
	"henghengman-desktop/backend/pkg/progress"
	"io"
	"log/slog"
	"os/exec"
	"strings"
)

type FFMpeg struct {
	inputs         []string
	output         string
	args           []string
	globalArgs     []string
	reportCallback progress.ReportCallback
	total          int64
}

func NewFFMpeg() *FFMpeg {
	return &FFMpeg{}
}

func (f *FFMpeg) SetInput(input []string) *FFMpeg {
	f.inputs = input
	return f
}

func (f *FFMpeg) AddInput(input string) *FFMpeg {
	f.inputs = append(f.inputs, input)
	return f
}

func (f *FFMpeg) SetOutput(output string) *FFMpeg {
	f.output = output
	return f
}

func (f *FFMpeg) SetProgressTotal(total int64) *FFMpeg {
	f.total = total
	return f
}

func (f *FFMpeg) AddArgs(args ...string) *FFMpeg {
	f.args = append(f.args, args...)
	return f
}

func (f *FFMpeg) GlobalArgs(args ...string) *FFMpeg {
	f.globalArgs = append(f.globalArgs, args...)
	return f
}

func (f *FFMpeg) Progress() *FFMpeg {
	return f.GlobalArgs("-progress", "pipe:1")
}

func (f *FFMpeg) Quiet() *FFMpeg {
	return f.GlobalArgs("-hide_banner", "-loglevel", "error")
}

func (f *FFMpeg) Override() *FFMpeg {
	return f.GlobalArgs("-y")
}

func (f *FFMpeg) SetReportCallback(reportCallback progress.ReportCallback) *FFMpeg {
	f.reportCallback = reportCallback
	return f
}

func (f *FFMpeg) checkError(_ context.Context, reader io.ReadCloser) chan error {
	errChan := make(chan error)
	go func() {
		defer close(errChan)
		sb := strings.Builder{}
		scanner := bufio.NewScanner(reader)
		for scanner.Scan() {
			line := scanner.Text()
			if strings.Contains(line, "Error") { // ffmepg运行期间检测到错误
				if strings.Contains(line, "No space left on device") { // 磁盘空间不足错误
					errChan <- errors.New("err_disk_full")
					return
				}
				sb.WriteString(line)
				sb.WriteRune('\n')
			}
		}
		errChan <- errors.New(sb.String())
		return
	}()
	return errChan
}

func (f *FFMpeg) Run(ctx context.Context) error {
	cmd := f.Build(ctx)
	// 捕获ffmpeg输出
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return err
	}
	defer stdoutPipe.Close()
	if f.reportCallback != nil {
		monitor := newFFMpegProgressMonitor(stdoutPipe, f.total, f.reportCallback)
		monitor.Start()
		defer monitor.Stop()
	}
	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		return err
	}
	defer stderrPipe.Close()
	errChan := f.checkError(ctx, stderrPipe)
	// 启动命令
	if err := cmd.Start(); err != nil {
		slog.WarnContext(ctx, "转换命令启动失败", "err", err, "cmd", cmd.String())
		// todo 使用time延时，防止卡死
		return <-errChan
	}
	// 等待命令完成
	if err := cmd.Wait(); err != nil {
		slog.WarnContext(ctx, "转换命令执行失败", "err", err, "cmd", cmd.String())
		return <-errChan
	}
	return nil
}

func (f *FFMpeg) Build(ctx context.Context) *exec.Cmd {
	var args []string
	for _, input := range f.inputs {
		args = append(args, "-i", input)
	}
	if f.reportCallback != nil {
		f.Progress().Quiet()
	}
	args = append(args, f.args...)
	args = append(args, f.output)
	args = append(args, f.globalArgs...)
	return exec.CommandContext(ctx, GetFFMpegBinPath(ctx), args...)
}
