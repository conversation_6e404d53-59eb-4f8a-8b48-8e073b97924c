package converter

import (
	"context"
	_ "embed"
	"heng<PERSON><PERSON>-desktop/backend/constant"
	"heng<PERSON><PERSON>-desktop/backend/pkg/errs"
	"log/slog"
	"path"
	"slices"
	"strconv"
	"strings"

	"github.com/gabriel-vasile/mimetype"
)

type Client struct {
}

func NewClient() *Client {
	return &Client{}
}

// NeedTranscode 判断是否需要转码（基于编码器和容器格式）
func (c *Client) NeedTranscode(codec string, targetFormat string) bool {
	codec = strings.ToLower(codec)
	targetFormat = strings.ToLower(targetFormat)
	supportedCodecs, ok := constant.FormatToCodec[targetFormat]
	if !ok {
		return true
	}
	for _, supported := range supportedCodecs {
		if codec == supported {
			return false
		}
	}
	return true
}

type computeDuration func(duration string)

func (c *Client) getTotalDuration(total *int64) computeDuration {
	return func(durationStr string) {
		duration, _ := strconv.ParseFloat(durationStr, 64)
		if duration > 0 {
			duration = duration * 1000000
		}
		if *total < int64(duration) {
			*total = int64(duration)
		}
	}
}

func (c *Client) Image(ctx context.Context, inputFilePath, outputDir, outputName string) error {
	builder := NewFFMpeg()
	builder.Override()
	builder = builder.AddInput(inputFilePath)
	// 不符合格式标记
	var outputFormatFound = true
	outputExt := path.Ext(outputName)
	for _, format := range constant.ImageOutputFormats {
		if format.Ext == outputExt {
			builder = builder.SetOutput(path.Join(outputDir, outputName+format.Ext))
			if len(format.Extra) > 0 {
				for _, s := range format.Extra {
					builder.AddArgs(s)
				}
			}
			outputFormatFound = false
			break
		}
	}
	if outputFormatFound {
		return errs.NewConvertFormatNotFoundError()
	}
	err := builder.Run(ctx)
	if err != nil {
		return errs.NewConvertError().Wrap(err)
	}
	return nil
}

func (c *Client) getFormatByExt(ext string) string {
	if !strings.HasPrefix(ext, ".") {
		ext = "." + ext
	}

	// 检查视频格式
	for _, format := range constant.VideoOutputFormats {
		if format.Ext == ext {
			return format.Value
		}
	}

	// 检查音频格式
	for _, format := range constant.AudioOutputFormats {
		if format.Ext == ext {
			return format.Value
		}
	}

	// 检查图片格式
	for _, format := range constant.ImageOutputFormats {
		if format.Ext == ext {
			return format.Value
		}
	}

	return ""
}

func (c *Client) Video(ctx context.Context, inputFilePath []string, outputDir, outputName string, opts ...OptionFunc) error {
	option := newOption(opts...)
	builder := NewFFMpeg()
	builder.Override()
	if option.reportCallback != nil {
		builder.SetReportCallback(option.reportCallback)
	}
	// 不符合格式标记
	var outputFormatFound = true
	outputExt := path.Ext(outputName)
	for _, format := range constant.VideoOutputFormats {
		if format.Ext == outputExt {
			builder.SetOutput(path.Join(outputDir, outputName+format.Ext))
			if len(format.Extra) > 0 {
				for _, s := range format.Extra {
					builder.AddArgs(s)
				}
			}
			outputFormatFound = false
			break
		}
	}
	if outputFormatFound {
		return errs.NewConvertFormatNotFoundError()
	}
	format := c.getFormatByExt(outputExt)
	videoCodec := false // 视频是否需要重新编码
	audioCodec := false // 音频是否需要重新编码
	hasVideo := false
	var total int64
	compute := c.getTotalDuration(&total)
	for _, filePath := range inputFilePath {
		builder = builder.AddInput(filePath)
		probeResult, err := GetFFProbeRes(ctx, filePath)
		if err != nil {
			return errs.NewFFProbeError().Wrap(err)
		}
		compute(probeResult.Format.Duration)
		for _, stream := range probeResult.Streams { // 判断是否需要重新编码
			switch stream.CodecType {
			case constant.MediaTypeVideo:
				if c.NeedTranscode(stream.CodecName, format) {
					videoCodec = true
				}
				hasVideo = true
				compute(stream.Duration)
			case constant.MediaTypeAudio:
				if c.NeedTranscode(stream.CodecName, format) {
					audioCodec = true
				}
				compute(stream.Duration)
			default:
			}
		}
	}
	builder = builder.SetProgressTotal(total)
	if !hasVideo {
		return errs.NewNoVideoError()
	}
	if videoCodec {
		builder = builder.AddArgs("-c:v", constant.FormatToEncoder[format])
	} else {
		builder = builder.AddArgs("-c:v", "copy")
	}
	if audioCodec {
		builder = builder.AddArgs("-c:a", constant.FormatToEncoder[format])
	} else {
		builder = builder.AddArgs("-c:a", "copy")
	}
	err := builder.Run(ctx)
	if err != nil {
		return errs.NewConvertError().Wrap(err)
	}
	return nil
}

func (c *Client) Audio(ctx context.Context, inputFilePath string, outputDir, outputName string, opts ...OptionFunc) error {
	option := newOption(opts...)
	builder := NewFFMpeg()
	builder.Override()
	if option.reportCallback != nil {
		builder.SetReportCallback(option.reportCallback)
	}
	// 不符合格式标记
	var outputFormatFound = true
	outputExt := path.Ext(outputName)
	for _, format := range constant.AudioOutputFormats {
		if format.Ext == outputExt {
			builder.SetOutput(path.Join(outputDir, outputName+format.Ext))
			if len(format.Extra) > 0 {
				for _, s := range format.Extra {
					builder.AddArgs(s)
				}
			}
			outputFormatFound = false
			break
		}
	}
	if outputFormatFound {
		return errs.NewConvertFormatNotFoundError()
	}
	builder = builder.AddInput(inputFilePath)
	probeResult, err := GetFFProbeRes(ctx, inputFilePath)
	if err != nil {
		return errs.NewFFProbeError().Wrap(err)
	}
	format := c.getFormatByExt(outputExt)
	hasAudio := false
	var total int64
	compute := c.getTotalDuration(&total)
	compute(probeResult.Format.Duration)
	for _, stream := range probeResult.Streams { // 判断是否需要重新编码
		switch stream.CodecType {
		case constant.MediaTypeAudio:
			if c.NeedTranscode(stream.CodecName, format) {
				builder.AddArgs("-c:a", constant.FormatToEncoder[format])
			} else {
				builder.AddArgs("-c:a", "copy")
			}
			hasAudio = true
			compute(stream.Duration)
		default:
		}
	}
	builder = builder.SetProgressTotal(total)
	if !hasAudio {
		return errs.NewNoAudioError()
	}
	err = builder.Run(ctx)
	if err != nil {
		return errs.NewConvertError().Wrap(err)
	}
	return nil
}

func (c *Client) getImageNativeStream(ctx context.Context, filePath string) ([]Stream, bool) {
	mt, err := mimetype.DetectFile(filePath)
	if err != nil {
		slog.Warn("mimetype解析文件失败", slog.Any("err", err), slog.Any("filePath", filePath))
		return nil, false
	}
	if mt.String() == "" {
		return nil, false
	}
	mts := strings.Split(mt.String(), "/")
	if len(mts) < 2 {
		return nil, false
	}
	if mts[0] != "image" {
		return nil, false
	}
	probeResult, err := GetFFProbeRes(ctx, filePath)
	if err != nil {
		return nil, false
	}
	if len(probeResult.Streams) == 0 {
		return nil, false
	}
	for i := range probeResult.Streams {
		probeResult.Streams[i].CodecType = constant.MediaTypeImage
	}
	return probeResult.Streams, true
}

func (c *Client) getFilesStream(ctx context.Context, filePaths []string) ([]Stream, []string, error) {
	streams := make([]Stream, 0)
	otherFiles := make([]string, 0)
	for _, filePath := range filePaths {
		imageStreams, ok := c.getImageNativeStream(ctx, filePath)
		if ok {
			streams = append(streams, imageStreams...)
			continue
		}
		probeResult, err := GetFFProbeRes(ctx, filePath)
		if err != nil {
			return nil, nil, err
		}
		if len(probeResult.Streams) > 0 {
			for _, stream := range probeResult.Streams {
				if slices.Contains([]string{constant.MediaTypeVideo, constant.MediaTypeAudio, constant.MediaTypeSubtitle}, stream.CodecType) {
					streams = append(streams, stream)
				}
			}
		} else {
			otherFiles = append(otherFiles, filePath)
		}
	}
	return streams, otherFiles, nil
}
