package converter

import (
	"context"
	"github.com/apparentlymart/go-userdirs/userdirs"
	"github.com/wailsapp/wails/v2/pkg/runtime"
	"henghengman-desktop/backend/constant"
	"path"
	goruntime "runtime"
)

const ffprobeToolName = "ffprobe"
const ffmpegToolName = "ffmpeg"

func GetFFProbeBinPath(ctx context.Context) string {
	name := ffprobeToolName
	if goruntime.GOOS == "windows" {
		name = name + ".exe"
	}
	home := constant.ToolsPath
	wailsEnv := runtime.Environment(ctx)
	if wailsEnv.BuildType != "dev" {
		dirs := userdirs.ForApp(constant.PackageName, constant.PackageName, constant.PackageName)
		home = dirs.DataHome()
		home = path.Join(home, constant.ToolsPath)
	}
	return path.Join(home, name)
}

func GetFFMpegBinPath(ctx context.Context) string {
	name := ffmpegToolName
	if goruntime.GOOS == "windows" {
		name = name + ".exe"
	}
	home := constant.ToolsPath
	wailsEnv := runtime.Environment(ctx)
	if wailsEnv.BuildType != "dev" {
		dirs := userdirs.ForApp(constant.PackageName, constant.PackageName, constant.PackageName)
		home = dirs.DataHome()
		home = path.Join(home, constant.ToolsPath)
	}
	return path.Join(home, name)
}
