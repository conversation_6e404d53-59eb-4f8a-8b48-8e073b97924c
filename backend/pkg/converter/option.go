package converter

import "henghengman-desktop/backend/pkg/progress"

type Option struct {
	reportCallback progress.ReportCallback
}

func newOption(opts ...OptionFunc) *Option {
	option := &Option{}
	for _, opt := range opts {
		opt(option)
	}
	return option
}

type OptionFunc func(option *Option)

func WithReportCallback(reportCallback progress.ReportCallback) OptionFunc {
	return func(option *Option) {
		option.reportCallback = reportCallback
	}
}
