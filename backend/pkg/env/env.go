package env

import (
	"os"
)

func GetLanguage() string {
	return os.Getenv("cat-language")
}

func SetLanguage(language string) {
	_ = os.Setenv("cat-language", language)
}

func GetSessionID() string {
	return os.Getenv("cat-session-id")
}

func SetSessionID(sessionID string) {
	_ = os.Setenv("cat-session-id", sessionID)
}

func SetDevelopment() {
	_ = os.Setenv("cat-development", "true")
}

func IsDevelopment() bool {
	return os.Getenv("cat-development") == "true"
}
