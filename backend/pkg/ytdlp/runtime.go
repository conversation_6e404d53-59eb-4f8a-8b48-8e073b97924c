package ytdlp

import (
	"context"
	"github.com/apparentlymart/go-userdirs/userdirs"
	"github.com/wailsapp/wails/v2/pkg/runtime"
	"henghengman-desktop/backend/constant"
	"path"
	goruntime "runtime"
)

const toolName = "yt-dlp"

func GetBinPath(ctx context.Context) string {
	name := toolName
	if goruntime.GOOS == "windows" {
		name = name + ".exe"
	}
	home := constant.ToolsPath
	wailsEnv := runtime.Environment(ctx)
	if wailsEnv.BuildType != "dev" {
		dirs := userdirs.ForApp(constant.PackageName, constant.PackageName, constant.PackageName)
		home = dirs.DataHome()
		home = path.Join(home, constant.ToolsPath)
	}
	return path.Join(home, name)
}
