package ytdlp

import (
	"context"
	"encoding/json"
	"fmt"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/pkg/errs"
	"henghengman-desktop/backend/pkg/utils"
	"henghengman-desktop/backend/types"
	"log/slog"
	"os/exec"
	"regexp"
	"slices"
	"strings"
	"sync/atomic"
	"time"

	"golang.org/x/sync/errgroup"
)

type Client struct {
	executableFilePath string
}

func NewClient(executableFilePath string) *Client {
	return &Client{
		executableFilePath: executableFilePath,
	}
}

func (c *Client) buildPostCmd(ctx context.Context, url string, opt *option) *exec.Cmd {
	args := []string{
		url,
		"--simulat",
		"--quie",
		"--dump-single-json",
		"--no-check-certificates",
		"--no-warnings",
		"--no-playlist",
		"--ignore-errors",
		"--ignore-config",
		"--no-cache-dir",
		"--prefer-insecure",
		"--extractor-args",
		"eneric:extract_flat=true",
		"--retries",
		"3",
		"--socket-timeout",
		"10",
		"--flat-playlist",
		"-no-write-comments",
	}
	switch opt.Proxy {
	case "system":
	case "direct":
		args = append(args, "--proxy", "")
	default:
		args = append(args, "--proxy", opt.Proxy)
	}
	return c.hideCmd(exec.CommandContext(ctx, c.executableFilePath, args...))
}

func (c *Client) buildPlayListCmd(ctx context.Context, url string, start, end int, opt *option) *exec.Cmd {
	args := []string{
		url,
		"--simulat",
		"--quie",
		"--dump-single-json",
		"--playlist-start",
		utils.IntToStr(start),
		"--playlist-end",
		utils.IntToStr(end),
		"--no-check-certificates",
		"--no-warnings",
		"--ignore-errors",
		"--ignore-config",
		"--no-cache-dir",
		"--prefer-insecure",
		"--extractor-args",
		"eneric:extract_flat=true",
		"--retries",
		"3",
		"--socket-timeout",
		"10",
		"--yes-playlist",
	}
	switch opt.Proxy {
	case "system":
	case "direct":
		//args = append(args, "--proxy", "")
	default:
		args = append(args, "--proxy", opt.Proxy)
	}
	return c.hideCmd(exec.CommandContext(ctx, c.executableFilePath, args...))
}

func (c *Client) getQualityNote(format Format) string {
	if format.Width != 0 && format.Height != 0 {
		return fmt.Sprintf("%dp", min(format.Width, format.Height))
	}
	if format.FormatNote != "" {
		return format.FormatNote
	}
	if format.Format != "" {
		re := regexp.MustCompile(`\d+p`)
		res := re.FindStringSubmatch(format.Format)
		if len(res) > 0 {
			return res[0]
		}
		if strings.Contains(format.Format, "high") {
			return "high"
		} else if strings.Contains(format.Format, "low") {
			return "low"
		}
		return format.Format
	}
	return ""
}

func (c *Client) parsePost(resp Resp) *types.PostResp {
	// 转换为 types.PostResp
	postResp := &types.PostResp{
		Text:     resp.Title,
		ID:       resp.Id,
		Overseas: 0, // TODO: 需要确定如何判断是否为海外资源
		Medias:   make([]*types.Media, 0),
	}
	if resp.Timestamp != 0 {
		postResp.CreateTime = time.Unix(int64(resp.Timestamp), 0).Format(time.DateTime)
	}

	// 创建媒体对象
	mediaMap := make(map[string]*types.Media)

	// 转换格式信息
	for _, format := range resp.Formats {
		if !slices.Contains([]string{"http", "https"}, format.Protocol) {
			continue
		}
		if format.VideoExt == "none" && format.AudioExt == "none" {
			continue
		}
		if format.Vcodec != "none" && format.Vcodec != "" { // 如果是视频格式
			media, ok := mediaMap[constant.MediaTypeVideo]
			if !ok {
				media = &types.Media{
					MediaType:  constant.MediaTypeVideo,
					PreviewURL: resp.Thumbnail,
				}
			}
			respFormat := types.Format{
				QualityNote: c.getQualityNote(format),
				VideoExt:    format.VideoExt,
				VideoSize:   format.Filesize,
				VideoURL:    format.Url,
			}
			if format.Acodec == "none" || format.Acodec == "" {
				respFormat.Separate = 1
			}
			media.Headers = format.HttpHeaders
			media.Formats = append(media.Formats, respFormat)
			mediaMap[constant.MediaTypeVideo] = media
		} else if (format.Acodec != "none" && format.Acodec != "") || (format.AudioExt != "none" && format.AudioExt != "") { // 如果是音频格式
			media, ok := mediaMap[constant.MediaTypeAudio]
			if !ok {
				media = &types.Media{
					MediaType:  constant.MediaTypeAudio,
					PreviewURL: resp.Thumbnail,
				}
			}
			respFormat := types.Format{
				QualityNote: c.getQualityNote(format),
				AudioExt:    format.AudioExt,
				AudioSize:   format.Filesize,
				AudioURL:    format.Url,
				Language:    format.Language,
			}
			media.Headers = format.HttpHeaders
			media.Formats = append(media.Formats, respFormat)
			mediaMap[constant.MediaTypeAudio] = media
		} else if format.VideoExt != "none" && format.VideoExt != "" {
			media, ok := mediaMap[constant.MediaTypeVideo]
			if !ok {
				media = &types.Media{
					MediaType:  constant.MediaTypeVideo,
					PreviewURL: resp.Thumbnail,
				}
			}
			respFormat := types.Format{
				QualityNote: c.getQualityNote(format),
				VideoExt:    format.VideoExt,
				VideoSize:   format.Filesize,
				VideoURL:    format.Url,
				Separate:    0,
			}
			media.Headers = format.HttpHeaders
			media.Formats = append(media.Formats, respFormat)
			mediaMap[constant.MediaTypeVideo] = media
		} else if resp.Thumbnail != "" {
			media, ok := mediaMap[constant.MediaTypeImage]
			if !ok {
				media = &types.Media{
					MediaType: constant.MediaTypeVideo,
				}
			}
			respFormat := types.Format{
				ImageURL: resp.Thumbnail,
			}
			media.Headers = format.HttpHeaders
			media.Formats = append(media.Formats, respFormat)
			mediaMap[constant.MediaTypeImage] = media
		}
	}
	for _, media := range mediaMap {
		media.Formats = utils.RemoveDuplicatesByKey(media.Formats, func(item types.Format) string {
			switch media.MediaType {
			case constant.MediaTypeVideo:
				return fmt.Sprintf("%s-%s-%s", constant.MediaTypeVideo, item.VideoExt, item.QualityNote)
			case constant.MediaTypeAudio:
				return fmt.Sprintf("%s-%s", constant.MediaTypeAudio, item.AudioExt)
			case constant.MediaTypeImage:
				return fmt.Sprintf("%s-%s", constant.MediaTypeImage, item.ImageURL)
			}
			return ""
		})
		postResp.Medias = append(postResp.Medias, media)
	}
	return postResp
}

func (c *Client) Post(ctx context.Context, url string, opts ...Option) (*types.PostResp, error) {
	opt := &option{}
	for _, o := range opts {
		o(opt)
	}
	cmd := c.buildPostCmd(ctx, url, opt)
	outputData, err := cmd.Output()
	if err != nil {
		slog.ErrorContext(ctx, "yt-dlp命令执行", "error", err)
		return nil, errs.NewExecError().Wrap(err)
	}
	var resp Resp
	err = json.Unmarshal(outputData, &resp)
	if err != nil {
		return nil, errs.NewSerializeError().Wrap(err)
	}
	//fmt.Println(utils.ToJson(resp))
	return c.parsePost(resp), nil
}

func (c *Client) PlayList(ctx context.Context, url, cursor string, opts ...Option) (*types.PlaylistResp, error) {
	opt := &option{}
	for _, o := range opts {
		o(opt)
	}
	index := 1
	if cursor != "" {
		var cursorData types.Cursor
		err := json.Unmarshal([]byte(utils.DecodeBase64(cursor)), &cursorData)
		if err != nil {
			return nil, errs.NewSerializeError().Wrap(err)
		}
		index = cursorData.Index
	}
	result := &types.PlaylistResp{
		HasMore: true,
	}
	wg, ctx := errgroup.WithContext(ctx)
	res := make([]*[]*types.PostResp, 0) // 使用二维数组存储每个协程的结果
	oneCount := 5
	count := constant.PlayListPageSize / oneCount
	u := atomic.Value{}
	for i := 0; i < count; i++ {
		i := i
		res = append(res, &[]*types.PostResp{})
		start := index
		end := start + oneCount - 1
		index = end + 1
		wg.Go(func() error {
			cmd := c.buildPlayListCmd(ctx, url, start, end, opt)
			slog.Info(cmd.String())
			outputData, err := cmd.Output()
			if err != nil {
				return errs.NewExecError().Wrap(err)
			}
			var resp Resp
			err = json.Unmarshal(outputData, &resp)
			if err != nil {
				return errs.NewSerializeError().Wrap(err)
			}
			u.CompareAndSwap(nil, &types.User{
				Username: resp.UploaderId,
			})
			if len(resp.Entries) == 0 {
				return nil
			}
			for _, entry := range resp.Entries {
				*res[i] = append(*res[i], c.parsePost(entry))
			}
			return nil
		})
	}
	if err := wg.Wait(); err != nil {
		return nil, err
	}
	for _, re := range res {
		result.Posts = append(result.Posts, *re...)
	}
	if len(result.Posts) == 0 {
		result.HasMore = false
	} else {
		cursorData := types.Cursor{
			Index:         index,
			LastExtractor: types.TargetLocal,
		}
		jsonData, err := json.Marshal(cursorData)
		if err != nil {
			return nil, errs.NewSerializeError().Wrap(err)
		}
		result.NextCursor = utils.EncodeBase64(string(jsonData))
	}
	result.User = u.Load().(*types.User)
	return result, nil
}
