package ytdlp

type Format struct {
	FormatId   string   `json:"format_id"`
	FormatNote string   `json:"format_note,omitempty"`
	Ext        string   `json:"ext"`
	Protocol   string   `json:"protocol"`
	Acodec     string   `json:"acodec,omitempty"`
	Vcodec     string   `json:"vcodec"`
	Url        string   `json:"url"`
	Width      int      `json:"width,omitempty"`
	Height     int      `json:"height,omitempty"`
	Fps        *float64 `json:"fps,omitempty"`
	Rows       int      `json:"rows,omitempty"`
	Columns    int      `json:"columns,omitempty"`
	Fragments  []struct {
		Url      string  `json:"url"`
		Duration float64 `json:"duration"`
	} `json:"fragments,omitempty"`
	AudioExt           string            `json:"audio_ext"`
	VideoExt           string            `json:"video_ext"`
	Vbr                *float64          `json:"vbr"`
	Abr                *float64          `json:"abr"`
	Tbr                *float64          `json:"tbr"`
	Resolution         string            `json:"resolution"`
	AspectRatio        *float64          `json:"aspect_ratio"`
	FilesizeApprox     *int              `json:"filesize_approx,omitempty"`
	HttpHeaders        map[string]string `json:"http_headers"`
	Format             string            `json:"format"`
	FormatIndex        interface{}       `json:"format_index"`
	ManifestUrl        string            `json:"manifest_url,omitempty"`
	Language           string            `json:"language,omitempty"`
	Preference         interface{}       `json:"preference"`
	Quality            float64           `json:"quality,omitempty"`
	HasDrm             bool              `json:"has_drm,omitempty"`
	SourcePreference   int               `json:"source_preference,omitempty"`
	NeedsTesting       bool              `json:"__needs_testing,omitempty"`
	LanguagePreference int               `json:"language_preference,omitempty"`
	Asr                *int              `json:"asr,omitempty"`
	Filesize           int               `json:"filesize,omitempty"`
	AudioChannels      *int              `json:"audio_channels,omitempty"`
	DynamicRange       *string           `json:"dynamic_range,omitempty"`
	Container          string            `json:"container,omitempty"`
	DownloaderOptions  struct {
		HttpChunkSize int `json:"http_chunk_size"`
	} `json:"downloader_options,omitempty"`
	Working bool `json:"__working,omitempty"`
}

type Resp struct {
	Id         string   `json:"id"`
	Title      string   `json:"title"`
	Formats    []Format `json:"formats"`
	Thumbnails []struct {
		Url        string `json:"url"`
		Preference int    `json:"preference"`
		Id         string `json:"id"`
		Height     int    `json:"height,omitempty"`
		Width      int    `json:"width,omitempty"`
		Resolution string `json:"resolution,omitempty"`
	} `json:"thumbnails"`
	Thumbnail         string        `json:"thumbnail"`
	Description       string        `json:"description"`
	ChannelId         string        `json:"channel_id"`
	ChannelUrl        string        `json:"channel_url"`
	Duration          float32       `json:"duration"`
	ViewCount         int           `json:"view_count"`
	AverageRating     interface{}   `json:"average_rating"`
	AgeLimit          int           `json:"age_limit"`
	WebpageUrl        string        `json:"webpage_url"`
	Categories        []string      `json:"categories"`
	Tags              []interface{} `json:"tags"`
	PlayableInEmbed   bool          `json:"playable_in_embed"`
	LiveStatus        string        `json:"live_status"`
	MediaType         string        `json:"media_type"`
	ReleaseTimestamp  interface{}   `json:"release_timestamp"`
	FormatSortFields  []string      `json:"_format_sort_fields"`
	AutomaticCaptions map[string][]struct {
		Ext         string `json:"ext"`
		Url         string `json:"url"`
		Name        string `json:"name"`
		Impersonate bool   `json:"impersonate"`
		YtDlpClient string `json:"__yt_dlp_client"`
	} `json:"automatic_captions"`
	Subtitles map[string][]struct {
		Ext         string `json:"ext"`
		Url         string `json:"url"`
		Name        string `json:"name"`
		Impersonate bool   `json:"impersonate"`
		YtDlpClient string `json:"__yt_dlp_client"`
	} `json:"subtitles"`
	CommentCount         int         `json:"comment_count"`
	Chapters             interface{} `json:"chapters"`
	Heatmap              interface{} `json:"heatmap"`
	LikeCount            int         `json:"like_count"`
	Channel              string      `json:"channel"`
	ChannelFollowerCount int         `json:"channel_follower_count"`
	Uploader             string      `json:"uploader"`
	UploaderId           string      `json:"uploader_id"`
	UploaderUrl          string      `json:"uploader_url"`
	UploadDate           string      `json:"upload_date"`
	Timestamp            float32     `json:"timestamp"`
	Availability         string      `json:"availability"`
	OriginalUrl          string      `json:"original_url"`
	WebpageUrlBasename   string      `json:"webpage_url_basename"`
	WebpageUrlDomain     string      `json:"webpage_url_domain"`
	Extractor            string      `json:"extractor"`
	ExtractorKey         string      `json:"extractor_key"`
	Playlist             interface{} `json:"playlist"`
	PlaylistIndex        interface{} `json:"playlist_index"`
	DisplayId            string      `json:"display_id"`
	Fulltitle            string      `json:"fulltitle"`
	DurationString       string      `json:"duration_string"`
	ReleaseYear          interface{} `json:"release_year"`
	IsLive               bool        `json:"is_live"`
	WasLive              bool        `json:"was_live"`
	RequestedSubtitles   interface{} `json:"requested_subtitles"`
	HasDrm               interface{} `json:"_has_drm"`
	Epoch                int         `json:"epoch"`
	Format               string      `json:"format"`
	FormatId             string      `json:"format_id"`
	Ext                  string      `json:"ext"`
	Protocol             string      `json:"protocol"`
	Language             string      `json:"language"`
	FormatNote           string      `json:"format_note"`
	FilesizeApprox       int         `json:"filesize_approx"`
	Tbr                  float64     `json:"tbr"`
	Width                int         `json:"width"`
	Height               int         `json:"height"`
	Resolution           string      `json:"resolution"`
	Fps                  float64     `json:"fps"`
	DynamicRange         string      `json:"dynamic_range"`
	Vcodec               string      `json:"vcodec"`
	Vbr                  float64     `json:"vbr"`
	StretchedRatio       interface{} `json:"stretched_ratio"`
	AspectRatio          float64     `json:"aspect_ratio"`
	Acodec               string      `json:"acodec"`
	Abr                  float64     `json:"abr"`
	Asr                  int         `json:"asr"`
	AudioChannels        int         `json:"audio_channels"`
	Type                 string      `json:"_type"`
	Entries              []Resp      `json:"entries"`
	Version              struct {
		Version        string      `json:"version"`
		CurrentGitHead interface{} `json:"current_git_head"`
		ReleaseGitHead string      `json:"release_git_head"`
		Repository     string      `json:"repository"`
	} `json:"_version"`
}
