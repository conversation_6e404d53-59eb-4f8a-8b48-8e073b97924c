//go:build !windows

package system

import (
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"syscall"
	"time"
)

func (s *System) InstallSoftware(filePath string) error {
	if filePath == "" {
		// 如果没有文件路径，直接退出应用
		// 注意：在 Wails 中，需要通过前端调用退出
		return nil
	}
	switch runtime.GOOS {
	case "darwin":
		// macOS: 使用 open 命令打开文件
		cmd := exec.Command("open", filePath)
		if err := cmd.Start(); err != nil {
			return fmt.Errorf("打开安装包失败: %w", err)
		}
	default:
		// Linux 或其他平台
		cmd := exec.Command(filePath)
		if err := cmd.Start(); err != nil {
			return fmt.Errorf("启动安装程序失败: %w", err)
		}

		time.Sleep(1 * time.Second)
	}

	return nil
}

// IsFileLocked 检查文件是否被占用 (Unix版本)
func (s *System) IsFileLocked(filePath string) (bool, error) {
	// 首先检查文件是否存在
	if _, err := os.Stat(filePath); err != nil {
		if os.IsNotExist(err) {
			return false, nil // 文件不存在，视为未被占用
		}
		return false, fmt.Errorf("检查文件存在性失败: %w", err)
	}

	if runtime.GOOS == "darwin" {
		return s.isFileLockedMac(filePath)
	}

	// Linux 或其他 Unix 系统
	return s.isFileLockedLinux(filePath)
}

// isFileLockedMac macOS系统下使用lsof命令检查文件是否被占用
func (s *System) isFileLockedMac(filePath string) (bool, error) {
	// 使用lsof命令检查文件是否被占用
	cmd := exec.Command("lsof", filePath)
	output, err := cmd.Output()

	if err != nil {
		// lsof命令执行出错通常意味着没有进程使用该文件
		return false, nil
	}

	// 如果lsof命令有输出，说明文件被占用
	isLocked := strings.TrimSpace(string(output)) != ""
	return isLocked, nil
}

// isFileLockedLinux Linux系统下检查文件是否被占用
func (s *System) isFileLockedLinux(filePath string) (bool, error) {
	// 尝试以读写方式打开文件
	file, err := os.OpenFile(filePath, os.O_RDWR, 0)
	if err != nil {
		// 检查是否是文件被占用的错误
		if pathError, ok := err.(*os.PathError); ok {
			if pathError.Err == syscall.EACCES ||
				pathError.Err == syscall.EPERM ||
				pathError.Err == syscall.EBUSY {
				return true, nil // 文件被占用
			}
		}
		return false, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	return false, nil // 能成功打开，说明文件未被占用
}
