package system

import (
	"fmt"
	"henghengman-desktop/backend/constant"
	"os"
	"path"
	"runtime"

	"github.com/apparentlymart/go-userdirs/userdirs"
)

// System 检测更新
type System struct {
}

func NewSystem() *System {
	return &System{}
}

func (s *System) GetTempDir() string {
	return path.Join(os.TempDir(), constant.PackageName)
}

func (s *System) GetToolsDir() string {
	dirs := userdirs.ForApp(constant.PackageName, constant.PackageName, constant.PackageName)
	return path.Join(dirs.DataHome(), "tools")
}
func (s *System) GetSuffix() string {
	if runtime.GOOS == "darwin" {
		if runtime.GOARCH == "arm64" {
			return "arm64.dmg"
		} else {
			return "x64.dmg"
		}
	} else {
		return "x64.exe"
	}
}

// CheckSoftwarePackageExists 检查指定版本的软件包是否存在
func (s *System) CheckSoftwarePackageExists(name, version string) (bool, string, error) {
	tempDir := s.GetTempDir()

	// 根据操作系统和架构确定文件后缀
	suffix := s.GetSuffix()
	installerName := fmt.Sprintf("%s_%s_%s", name, version, suffix)
	installerPath := path.Join(tempDir, installerName)

	// 检查文件是否存在
	_, err := os.Stat(installerPath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, "", nil
		}
		return false, "", err
	}

	return true, installerPath, nil
}
