//go:build windows

package system

import (
	"fmt"
	"os"
	"os/exec"
	"syscall"
)

// InstallSoftware 安装软件包
func (s *System) InstallSoftware(filePath string) error {
	if filePath == "" {
		// 如果没有文件路径，直接退出应用
		// 注意：在 Wails 中，需要通过前端调用退出
		return nil
	}

	// Windows: 直接执行安装包
	cmd := exec.Command(filePath)
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: syscall.CREATE_NEW_PROCESS_GROUP,
	}
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("启动安装程序失败: %w", err)
	}

	return nil
}

// IsFileLocked 检查文件是否被占用 (Windows版本)
func (s *System) IsFileLocked(filePath string) (bool, error) {
	// 首先检查文件是否存在
	if _, err := os.Stat(filePath); err != nil {
		if os.IsNotExist(err) {
			return false, nil // 文件不存在，视为未被占用
		}
		return false, fmt.Errorf("检查文件存在性失败: %w", err)
	}

	// 尝试以读写方式打开文件
	file, err := os.OpenFile(filePath, os.O_RDWR, 0)
	if err != nil {
		// 检查是否是文件被占用的错误
		if pathError, ok := err.(*os.PathError); ok {
			if pathError.Err == syscall.EACCES ||
				pathError.Err == syscall.EPERM ||
				pathError.Err == syscall.EBUSY {
				return true, nil // 文件被占用
			}
		}
		return false, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	return false, nil // 能成功打开，说明文件未被占用
}
