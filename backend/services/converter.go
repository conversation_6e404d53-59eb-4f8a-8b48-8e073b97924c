package services

import (
	"context"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/pkg/converter"
	"henghengman-desktop/backend/pkg/errs"
	"henghengman-desktop/backend/pkg/task"
	"henghengman-desktop/backend/types"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

type ConverterService struct {
	ctx             context.Context
	converterClient *converter.Client
	taskManager     *task.Manager
}

func NewConverterService(taskManager *task.Manager) *ConverterService {
	return &ConverterService{
		converterClient: converter.NewClient(),
		taskManager:     taskManager,
	}
}

func (s *ConverterService) Start(ctx context.Context) {
	s.ctx = ctx
}

func (s *ConverterService) Submit(req *types.ConverterTask) (string, error) {
	if req.InputFilePath == nil || len(req.InputFilePath) == 0 {
		return "", errs.NewInvalidParameterError("inputFilePath")
	}
	if req.OutputDir == "" {
		return "", errs.NewInvalidParameterError("outputDir")
	}
	if req.OutputName == "" {
		return "", errs.NewInvalidParameterError("outputName")
	}
	return task.NewStreamPromise[types.ConverterProgress](s.ctx, func(ctx context.Context, taskID string, resolve task.Resolve[types.ConverterProgress], reject task.Reject) error {
		return s.convert(ctx, taskID, req, resolve, reject)
	}).Then(func(taskID string, data types.ConverterProgress) {
		runtime.EventsEmit(s.ctx, constant.EventTaskConverterProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Data:   data,
		})
	}).Catch(func(taskID string, err error) {
		runtime.EventsEmit(s.ctx, constant.EventTaskConverterProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Error:  err.Error(),
		})
	}).Finally(func(ctx context.Context, taskID string) {
		runtime.EventsEmit(s.ctx, constant.EventTaskConverterProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Done:   true,
		})
	}).OnRegister(s.taskManager.Register).Start(), nil
}

func (s *ConverterService) Cancel(taskID string) {
	s.taskManager.Cancel(taskID)
}

func (s *ConverterService) convert(ctx context.Context, taskID string, req *types.ConverterTask, resolve task.Resolve[types.ConverterProgress], reject task.Reject) error {
	switch req.Type {
	case types.ConverterTypeVideo:
		return s.converterClient.Video(ctx, req.InputFilePath, req.OutputDir, req.OutputName, converter.WithReportCallback(func(total int64, done int64, speed int64, remainingTime int64) {
			resolve(taskID, types.ConverterProgress{
				Total:         total,
				Speed:         speed,
				RemainingTime: remainingTime,
			})
		}))
	case types.ConverterTypeAudio:
		return s.converterClient.Audio(ctx, req.InputFilePath[0], req.OutputDir, req.OutputName, converter.WithReportCallback(func(total int64, done int64, speed int64, remainingTime int64) {
			resolve(taskID, types.ConverterProgress{
				Total:         total,
				Speed:         speed,
				RemainingTime: remainingTime,
			})
		}))
	case types.ConverterTypeImage:
		return s.converterClient.Image(ctx, req.InputFilePath[0], req.OutputDir, req.OutputName)
	}
	return errs.NewUnsupportedConvertTypeError()
}
