package services

import (
	"context"
	"github.com/wailsapp/wails/v2/pkg/runtime"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/storage"
)

type AppService struct {
	pref *storage.PreferencesStorage
	ctx  context.Context
}

func NewAppService(pref *storage.PreferencesStorage) *AppService {
	return &AppService{
		pref: pref,
	}
}

// Start 启动应用
func (s *AppService) Start(ctx context.Context) {
	s.ctx = ctx
	runtime.EventsEmit(ctx, constant.EventAppStart)
}

// OnDomReady 应用DOM准备就绪
func (s *AppService) OnDomReady() {
	runtime.EventsEmit(s.ctx, constant.EventAppDomReady)
}

// OnShutdown 应用关闭
func (s *AppService) OnShutdown() {
	runtime.EventsEmit(s.ctx, constant.EventAppShutdown)
}

// OnBeforeClose 应用关闭前
func (s *AppService) OnBeforeClose() {
	runtime.EventsEmit(s.ctx, constant.EventAppBeforeClose)
}

// Quit 退出应用
func (s *AppService) Quit() {
	runtime.Quit(s.ctx)
}
