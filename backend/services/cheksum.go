package services

import (
	"context"
	"crypto/rand"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/pkg/diskerror"
	"henghengman-desktop/backend/pkg/errs"
	"henghengman-desktop/backend/pkg/progress"
	"henghengman-desktop/backend/pkg/task"
	"henghengman-desktop/backend/pkg/utils"
	"henghengman-desktop/backend/types"
	"io"
	"os"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

type ChecksumService struct {
	ctx         context.Context
	taskManager *task.Manager
}

func NewChecksumService(taskManager *task.Manager) *ChecksumService {
	service := &ChecksumService{
		taskManager: taskManager,
	}
	return service
}

func (s *ChecksumService) Start(ctx context.Context) {
	s.ctx = ctx
}

func (s *ChecksumService) AlterMD5(path string) string {
	return task.NewStreamPromise(s.ctx, func(ctx context.Context, taskID string, resolve task.Resolve[types.ChecksumStatus], reject task.Reject) error {
		return s.alterMD5(taskID, path, resolve)
	}).Then(func(taskID string, data types.ChecksumStatus) {
		runtime.EventsEmit(s.ctx, constant.EventTaskAlterMD5StatusKey, types.JSStreamResp{
			TaskID: taskID,
			Data:   data,
		})
	}).Catch(func(taskID string, err error) {
		runtime.EventsEmit(s.ctx, constant.EventTaskAlterMD5StatusKey, types.JSStreamResp{
			TaskID: taskID,
			Error:  err.Error(),
		})
	}).Finally(func(ctx context.Context, taskID string) {
		runtime.EventsEmit(s.ctx, constant.EventTaskAlterMD5StatusKey, types.JSStreamResp{
			TaskID: taskID,
			Done:   true,
		})
	}).OnRegister(s.taskManager.Register).Start()
}

func (s *ChecksumService) Cancel(taskID string) {
	s.taskManager.Cancel(taskID)
}

func (s *ChecksumService) progressCallback(taskID string) progress.ReportCallback {
	return func(total, done, speed, remainingTime int64) {
		runtime.EventsEmit(s.ctx, constant.EventTaskAlterMD5ProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Data: types.ChecksumProgress{
				Total:         total,
				Speed:         speed,
				RemainingTime: remainingTime,
			},
		})
	}
}

// AlterMD5 修改文件的 MD5 值
func (s *ChecksumService) alterMD5(taskID, path string, resolve task.Resolve[types.ChecksumStatus]) error {
	// 进度监听
	length, err := utils.GetFileLenght(path)
	if err != nil {
		return errs.NewGetFileInfoError().Wrap(err)
	}
	ag := progress.NewAggregator(length*2+1, s.progressCallback(taskID))
	ag.Start()
	defer ag.Stop()
	file, err := os.OpenFile(path, os.O_RDWR, os.ModePerm)
	if err != nil {
		return errs.NewGetFileInfoError().Wrap(err)
	}
	defer file.Close()
	// 计算旧md5值
	oldVal, err := utils.MD5FromReader(progress.NewReader(file, ag.Report))
	if err != nil {
		return errs.NewReadFileError().Wrap(err)
	}
	resolve(taskID, types.ChecksumStatus{
		OldMD5: oldVal,
	})
	// 获取填充一个字节
	data := make([]byte, 1)
	_, _ = rand.Read(data)
	_, err = file.Seek(0, io.SeekEnd)
	if err != nil {
		return errs.NewReadFileError().Wrap(err)
	}
	_, err = file.Write(data)
	if err != nil {
		return errs.NewWriteFileError().Wrap(err)
	}
	// 计算新md5值
	_, err = file.Seek(0, io.SeekStart)
	if err != nil {
		return errs.NewReadFileError().Wrap(err)
	}
	newVal, err := utils.MD5FromReader(progress.NewReader(file, ag.Report))
	if err != nil {
		return errs.NewReadFileError().Wrap(err)
	}
	resolve(taskID, types.ChecksumStatus{
		OldMD5: oldVal,
		NewMD5: newVal,
	})
	return nil
}

func (s *ChecksumService) GetMD5(path string) (string, error) {
	val, err := utils.MD5FromFile(path)
	if err != nil {
		if os.IsNotExist(err) {
			return "", errs.NewFileNotFoundError(path).Wrap(err)
		}
		if diskerror.IsDiskFull(err) {
			return "", errs.NewDiskFullError().Wrap(err)
		}
		if diskerror.IsPermission(err) {
			return "", errs.NewDiskPermissionError().Wrap(err)
		}
		return "", errs.NewGetMD5Error().Wrap(err)
	}
	return val, nil
}
