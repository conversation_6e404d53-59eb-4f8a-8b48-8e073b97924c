package services

import (
	"context"
	"github.com/apparentlymart/go-userdirs/userdirs"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/pkg/ytdlp"
	"henghengman-desktop/backend/storage"
	"henghengman-desktop/backend/types"
)

type PreferencesService struct {
	pref *storage.PreferencesStorage
	ctx  context.Context
}

func NewPreferencesService(pref *storage.PreferencesStorage) *PreferencesService {
	return &PreferencesService{
		pref: pref,
	}
}

func (s *PreferencesService) Start(ctx context.Context) {
	s.ctx = ctx
}

// GetPreferences 获取配置
func (s *PreferencesService) GetPreferences() types.Preferences {
	return s.pref.GetPreferences()
}

// SetPreferences 保存配置
func (s *PreferencesService) SetPreferences(req types.Preferences) error {
	return s.pref.SetPreferences(&req)
}

// RestorePreferences 重置配置为默认值
func (s *PreferencesService) RestorePreferences() types.Preferences {
	return s.pref.RestoreDefault()
}

func (s *PreferencesService) GetConfigPath() []string {
	dirs := userdirs.ForApp(constant.PackageName, constant.PackageName, constant.PackageName)
	return []string{
		dirs.ConfigHome(),
		dirs.DataHome(),
		dirs.CacheDir,
		ytdlp.GetBinPath(s.ctx),
	}
}
