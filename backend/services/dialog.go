package services

import (
	"context"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

type DialogService struct {
	ctx context.Context
}

func NewDialogService() *DialogService {
	service := &DialogService{}
	return service
}

func (s *DialogService) Start(ctx context.Context) {
	s.ctx = ctx
}

// PickDirectory opens a directory picker dialog and returns the selected directory path
func (s *DialogService) PickDirectory(title string, defaultPath string) (string, error) {
	options := runtime.OpenDialogOptions{
		Title:            title,
		DefaultDirectory: defaultPath,
	}

	return runtime.OpenDirectoryDialog(s.ctx, options)
}

// PickFile opens a file picker dialog and returns the selected file path
func (s *DialogService) PickFile(title string, defaultPath string, filters []runtime.FileFilter) (string, error) {
	options := runtime.OpenDialogOptions{
		Title:            title,
		DefaultDirectory: defaultPath,
		Filters:          filters,
	}

	return runtime.OpenFileDialog(s.ctx, options)
}

// PickMultipleFiles opens a file picker dialog for multiple files and returns the selected file paths
func (s *DialogService) PickMultipleFiles(title string, defaultPath string, filters []runtime.FileFilter) ([]string, error) {
	options := runtime.OpenDialogOptions{
		Title:            title,
		DefaultDirectory: defaultPath,
		Filters:          filters,
	}

	return runtime.OpenMultipleFilesDialog(s.ctx, options)
}

// SaveFile opens a save file dialog and returns the selected file path
func (s *DialogService) SaveFile(title string, defaultPath string, defaultFilename string, filters []runtime.FileFilter) (string, error) {
	options := runtime.SaveDialogOptions{
		Title:            title,
		DefaultDirectory: defaultPath,
		DefaultFilename:  defaultFilename,
		Filters:          filters,
	}

	return runtime.SaveFileDialog(s.ctx, options)
}

// ShowMessage displays a message dialog
func (s *DialogService) ShowMessage(title string, message string, dialogType string) (string, error) {
	var msgType runtime.DialogType
	switch dialogType {
	case "info":
		msgType = runtime.InfoDialog
	case "warning":
		msgType = runtime.WarningDialog
	case "error":
		msgType = runtime.ErrorDialog
	case "question":
		msgType = runtime.QuestionDialog
	default:
		msgType = runtime.InfoDialog
	}

	options := runtime.MessageDialogOptions{
		Type:    msgType,
		Title:   title,
		Message: message,
	}

	return runtime.MessageDialog(s.ctx, options)
}
