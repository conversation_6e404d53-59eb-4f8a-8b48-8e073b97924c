package services

import (
	"context"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/pkg/chat"
	"henghengman-desktop/backend/pkg/errs"
	"henghengman-desktop/backend/pkg/selector"
	"henghengman-desktop/backend/pkg/semaphore"
	"henghen<PERSON>-desktop/backend/pkg/task"
	"henghengman-desktop/backend/pkg/utils"
	"henghengman-desktop/backend/pkg/ytdlp"
	"henghengman-desktop/backend/storage"
	"henghengman-desktop/backend/types"
	"log/slog"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

type ExtractService struct {
	ctx         context.Context
	pref        *storage.PreferencesStorage
	chatClient  *chat.Client
	ytdlpClient *ytdlp.Client
	sem         *semaphore.Semaphore
	taskManager *task.Manager
}

func NewExtractService(pref *storage.PreferencesStorage, taskManager *task.Manager) *ExtractService {
	return &ExtractService{
		pref:        pref,
		sem:         semaphore.NewSemaphore(3),
		taskManager: taskManager,
	}
}

func (s *ExtractService) Start(ctx context.Context) {
	s.ctx = ctx
	s.chatClient = chat.NewClient(s.pref.GetPreferences().Server.BaseApi)
	s.ytdlpClient = ytdlp.NewClient(ytdlp.GetBinPath(ctx))
}

func (s *ExtractService) Post(urls []string) []string {
	taskIDs := make([]string, 0, len(urls))
	for _, url := range urls {
		taskID := task.NewStreamPromise(s.ctx, func(ctx context.Context, taskID string, resolve task.Resolve[types.PostResp], _ task.Reject) error {
			return s.post(ctx, taskID, url, resolve)
		}).Then(func(taskID string, data types.PostResp) {
			runtime.EventsEmit(s.ctx, constant.EventTaskExtractPostResp, types.JSStreamResp{
				TaskID: taskID,
				Data:   data,
			})
		}).Catch(func(taskID string, err error) {
			runtime.EventsEmit(s.ctx, constant.EventTaskExtractPostResp, types.JSStreamResp{
				TaskID: taskID,
				Error:  err.Error(),
			})
		}).Finally(func(ctx context.Context, taskID string) {
			runtime.EventsEmit(s.ctx, constant.EventTaskExtractPostResp, types.JSStreamResp{
				TaskID: taskID,
				Done:   true,
			})
		}).OnRegister(s.taskManager.Register).Start()
		taskIDs = append(taskIDs, taskID)
	}
	return taskIDs
}

func (s *ExtractService) Cancel(taskID string) {
	s.taskManager.Cancel(taskID)
}

func (s *ExtractService) post(ctx context.Context, taskID string, url string, resolve task.Resolve[types.PostResp]) error {
	var resp *types.PostResp
	var err error
	strategy := selector.SelectStrategy(s.pref.GetPreferences().App.ExtractRule, url, "post")
	slog.InfoContext(ctx, "提取规则", "strategy", strategy.Name, "url", url)
	catch := false
	target := strategy.Try
	for {
		if target == types.TargetLocal {
			// 提取前判断是否可以提取
			membership := s.chatClient.IsMembership(s.pref.GetPreferences().User)
			if !membership { // 不是会员，检测是否还有剩余提取次数
				ok, ableErr := s.chatClient.ExtractEligibility(ctx, chat.WithSessionID(s.pref.GetPreferences().User.SessionID))
				if ableErr != nil {
					slog.ErrorContext(ctx, "是否可提取", "error", err)
				}
				if !ok {
					return errs.NewPaymentRequiredError().Wrap(ableErr)
				}
			}
			// 提取
			resp, err = s.ytdlpClient.Post(ctx, url, ytdlp.WithProxy(utils.GetProxy(s.pref.GetPreferences().Network)))
			if !membership && err == nil { // 不是会员，提取成功异步上报
				go func() {
					err := s.chatClient.ExtractBilling(ctx, chat.WithSessionID(s.pref.GetPreferences().User.SessionID))
					if err != nil {
						slog.ErrorContext(ctx, "本地提取上报", "error", err)
					}
				}()
			}
		} else {
			resp, err = s.chatClient.Post(ctx, url, chat.WithSemaphore(s.sem), chat.WithSessionID(s.pref.GetPreferences().User.SessionID))
		}
		if err != nil {
			if catch { // 已尝试过catch
				return err
			}
			if strategy.Catch != "" { // 尝试catch
				target = strategy.Catch
				catch = true
				continue
			}
			return err
		}
		resolve(taskID, *resp)
		return nil
	}
}

func (s *ExtractService) Playlist(req types.ExtractReq) (*types.PlaylistResp, error) {
	if req.URL == "" {
		return nil, errs.NewInvalidParameterError("url")
	}
	ctx, cancel := context.WithCancel(s.ctx)
	defer cancel()
	var resp *types.PlaylistResp
	var err error
	strategy := selector.SelectStrategy(s.pref.GetPreferences().App.ExtractRule, req.URL, "playlist")
	slog.InfoContext(ctx, "提取规则", "strategy", strategy.Name, "url", req.URL)
	if strategy.Try == types.TargetLocal {
		resp, err = s.ytdlpClient.PlayList(ctx, req.URL, req.Cursor, ytdlp.WithProxy(utils.GetProxy(s.pref.GetPreferences().Network)))
	} else {
		resp, err = s.chatClient.PlayList(ctx, req.URL, chat.WithSemaphore(s.sem), chat.WithSessionID(s.pref.GetPreferences().User.SessionID))
	}
	if err != nil {
		return nil, err
	}
	return resp, nil
}
