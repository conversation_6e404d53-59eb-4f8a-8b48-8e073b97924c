package services

import (
	"context"
	"fmt"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/pkg/chat"
	"henghengman-desktop/backend/pkg/downloader"
	"henghengman-desktop/backend/pkg/errs"
	"henghengman-desktop/backend/pkg/progress"
	"henghengman-desktop/backend/pkg/system"
	"henghengman-desktop/backend/pkg/task"
	"henghengman-desktop/backend/storage"
	"henghengman-desktop/backend/types"
	"log/slog"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	goruntime "runtime"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

const ytDlpBinName = "yt-dlp"

// UpdateService 检测更新
type UpdateService struct {
	pref       *storage.PreferencesStorage
	ctx        context.Context
	system     *system.System
	downloader *downloader.Client
	chatClient *chat.Client
}

func NewUpdateService(pref *storage.PreferencesStorage) *UpdateService {
	return &UpdateService{
		pref:       pref,
		system:     system.NewSystem(),
		chatClient: chat.NewClient(pref.GetPreferences().Server.BaseApi),
	}
}

func (s *UpdateService) Start(ctx context.Context) {
	s.ctx = ctx
}

// SubmitDownloadAppPackage 下载哼哼猫安装包
func (s *UpdateService) SubmitDownloadAppPackage(downloadURL, version string) string {
	return task.NewStreamPromise[types.DownloadProgress](s.ctx, func(ctx context.Context, taskID string, resolve task.Resolve[types.DownloadProgress], reject task.Reject) error {
		return s.downloadAppPackage(taskID, downloadURL, version, resolve)
	}).Then(func(taskID string, data types.DownloadProgress) {
		runtime.EventsEmit(s.ctx, constant.EventTaskDownloadProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Data:   data,
		})
	}).Catch(func(taskID string, err error) {
		runtime.EventsEmit(s.ctx, constant.EventTaskDownloadProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Error:  err.Error(),
		})
	}).Finally(func(ctx context.Context, taskID string) {
		runtime.EventsEmit(s.ctx, constant.EventTaskDownloadProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Done:   true,
		})
	}).Start()
}

func (s *UpdateService) downloadAppPackage(taskID, downloadURL, version string, resolve task.Resolve[types.DownloadProgress]) error {
	totalProgress := progress.NewAggregator(0, func(total int64, done int64, speed int64, remainingTime int64) {
		resolve(taskID, types.DownloadProgress{
			Total:         total,
			Speed:         speed,
			RemainingTime: remainingTime,
		})
	})
	totalProgress.Start()
	defer totalProgress.Stop()
	installerName := fmt.Sprintf("%s_%s_%s", s.pref.GetPreferences().App.Name, version, s.system.GetSuffix())
	err := s.downloader.Download(s.ctx, downloadURL, s.system.GetTempDir(), installerName, downloader.WithMonitor(totalProgress))
	if err != nil {
		return err
	}
	exists, _ := s.CheckAppPackageExists(version)
	if !exists {
		return errs.NewClientError(errs.ClientCodePackageNotFoundError)
	}
	return nil
}

// CheckAppPackageExists 检查指定版本的哼哼猫安装包是否已下载到本地
func (s *UpdateService) CheckAppPackageExists(version string) (bool, string) {
	ok, filePath, err := s.system.CheckSoftwarePackageExists(s.pref.GetPreferences().App.Name, version)
	if err != nil {
		return false, ""
	}
	return ok, filePath
}

// InstallSoftware 安装软件包
func (s *UpdateService) InstallSoftware(filePath string) error {
	return s.system.InstallSoftware(filePath)
}

// CheckAndUpdateComponent 检查并更新组件
func (s *UpdateService) CheckAndUpdateComponent() error {
	info, err := s.chatClient.GetYtDlpInfo(s.ctx)
	if err != nil {
		return errs.NewComponentInfoError().Wrap(err)
	}

	currentVersion := s.pref.GetPreferences().App.ComponentVersion
	if s.versionCompare(currentVersion, info.Version) >= 0 {
		slog.InfoContext(s.ctx, "组件版本已是最新",
			slog.String("current", currentVersion),
			slog.String("latest", info.Version))
		return nil
	}

	slog.InfoContext(s.ctx, "发现新版本组件，开始更新",
		slog.String("current", currentVersion),
		slog.String("latest", info.Version))

	// 根据操作系统选择下载URL和文件名
	downloadURL, ytDlpBinName := s.getDownloadInfo(info)
	if downloadURL == "" {
		return errs.NewUnsupportedOSError(goruntime.GOOS)
	}

	// 确保临时目录存在
	tempDir := s.system.GetTempDir()
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return errs.NewWriteFileError().Wrap(err)
	}

	// 下载组件
	if err := s.downloadComponent(downloadURL, tempDir, ytDlpBinName); err != nil {
		return err // downloadComponent 内部已经处理了错误类型
	}

	// 安装组件
	if err := s.installComponent(tempDir, ytDlpBinName); err != nil {
		return err // installComponent 内部已经处理了错误类型
	}

	// 更新组件版本信息
	if err := s.updateComponentVersion(info.Version); err != nil {
		slog.WarnContext(s.ctx, "更新组件版本信息失败", slog.Any("err", err))
		// 不返回错误，因为组件已经成功安装
	}

	slog.InfoContext(s.ctx, "组件更新成功", slog.String("version", info.Version))
	return nil
}

// getDownloadInfo 根据操作系统获取下载信息
func (s *UpdateService) getDownloadInfo(info *chat.GetYtDlpInfoResp) (string, string) {
	switch goruntime.GOOS {
	case "darwin":
		return info.DownloadUrls.MacOS, ytDlpBinName
	case "windows":
		return info.DownloadUrls.Windows, fmt.Sprintf("%s.exe", ytDlpBinName)
	case "linux":
		// Linux通常使用与macOS相同的二进制文件
		return info.DownloadUrls.MacOS, ytDlpBinName
	default:
		return "", ""
	}
}

// downloadComponent 下载组件，带重试机制
func (s *UpdateService) downloadComponent(downloadURL, tempDir, fileName string) error {
	filePath := path.Join(tempDir, fileName)

	// 检查是否已存在
	if _, err := os.Stat(filePath); err == nil {
		slog.InfoContext(s.ctx, "组件文件已存在，跳过下载", slog.String("path", filePath))
		return nil
	}

	const maxRetries = 3
	const baseDelay = time.Second * 2

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			delay := time.Duration(attempt) * baseDelay
			slog.InfoContext(s.ctx, "重试下载",
				slog.Int("attempt", attempt+1),
				slog.Duration("delay", delay))

			select {
			case <-s.ctx.Done():
				return s.ctx.Err()
			case <-time.After(delay):
			}
		}

		err := s.downloader.Download(s.ctx, downloadURL, tempDir, fileName)
		if err == nil {
			slog.InfoContext(s.ctx, "组件下载成功", slog.String("path", filePath))
			return nil
		}

		slog.WarnContext(s.ctx, "下载yt-dlp失败",
			slog.Int("attempt", attempt+1),
			slog.Int("maxRetries", maxRetries),
			slog.Any("err", err))

		if attempt == maxRetries-1 {
			return errs.NewDownloadRetryExceededError(maxRetries).Wrap(err)
		}
	}
	return nil
}

// installComponent 安装组件到目标目录
func (s *UpdateService) installComponent(tempDir, fileName string) error {
	sourcePath := path.Join(tempDir, fileName)
	targetDir := s.system.GetToolsDir()
	targetPath := path.Join(targetDir, fileName)

	// 确保目标目录存在
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return errs.NewWriteFileError().Wrap(err)
	}

	const maxWaitTime = time.Minute * 5    // 最大等待时间
	const checkInterval = time.Second * 10 // 检查间隔
	startTime := time.Now()

	for {
		// 检查是否超时
		if time.Since(startTime) > maxWaitTime {
			return errs.NewFileLockTimeoutError()
		}

		// 检查源文件是否被锁定
		locked, err := s.system.IsFileLocked(sourcePath)
		if err != nil {
			return errs.NewFileLockCheckError().Wrap(err)
		}
		if locked {
			slog.InfoContext(s.ctx, "源文件被锁定，等待解锁", slog.String("path", sourcePath))
			select {
			case <-s.ctx.Done():
				return s.ctx.Err()
			case <-time.After(checkInterval):
				continue
			}
		}

		// 执行原子性文件替换
		if err := s.atomicFileReplace(sourcePath, targetPath); err != nil {
			// 如果是因为目标文件被锁定，继续等待
			if errs.IsFileLockError(err) {
				slog.InfoContext(s.ctx, "目标文件被锁定，等待解锁", slog.String("path", targetPath))
				select {
				case <-s.ctx.Done():
					return s.ctx.Err()
				case <-time.After(checkInterval):
					continue
				}
			}
			return err
		}

		slog.InfoContext(s.ctx, "组件安装成功",
			slog.String("source", sourcePath),
			slog.String("target", targetPath))
		return nil
	}
}

// atomicFileReplace 原子性地替换文件
func (s *UpdateService) atomicFileReplace(sourcePath, targetPath string) error {
	targetDir := path.Dir(targetPath)
	fileName := path.Base(targetPath)
	tempPath := path.Join(targetDir, fileName+".new")

	// 第一步：将源文件移动到目标目录，命名为 .new 后缀
	if err := os.Rename(sourcePath, tempPath); err != nil {
		return errs.NewWriteFileError().Wrap(err)
	}

	// 确保在出错时清理临时文件
	defer func() {
		if _, err := os.Stat(tempPath); err == nil {
			os.Remove(tempPath)
		}
	}()

	// 第二步：检查目标文件是否存在并被锁定
	if _, err := os.Stat(targetPath); err == nil {
		targetLocked, err := s.system.IsFileLocked(targetPath)
		if err != nil {
			return errs.NewFileLockCheckError().Wrap(err)
		}
		if targetLocked {
			// 将临时文件移回原位置
			os.Rename(tempPath, sourcePath)
			return errs.NewFileLockError(targetPath)
		}

		// 删除已存在的目标文件
		if err := os.Remove(targetPath); err != nil {
			// 将临时文件移回原位置
			os.Rename(tempPath, sourcePath)
			return errs.NewWriteFileError().Wrap(err)
		}
	}

	// 第三步：将临时文件重命名为目标文件名
	if err := os.Rename(tempPath, targetPath); err != nil {
		// 重命名失败，将临时文件移回原位置
		os.Rename(tempPath, sourcePath)
		return errs.NewWriteFileError().Wrap(err)
	}

	return nil
}

// updateComponentVersion 更新组件版本信息
func (s *UpdateService) updateComponentVersion(version string) error {
	return s.pref.UpdatePreferences(map[string]any{
		"app.componentVersion": version,
	})
}

func (s *UpdateService) versionCompare(v1, v2 string) int {
	v1Parts := strings.Split(v1, ".")
	v2Parts := strings.Split(v2, ".")

	maxLen := len(v1Parts)
	if len(v2Parts) > maxLen {
		maxLen = len(v2Parts)
	}

	for i := 0; i < maxLen; i++ {
		var v1Part, v2Part int
		if i < len(v1Parts) {
			v1Part, _ = strconv.Atoi(v1Parts[i])
		}
		if i < len(v2Parts) {
			v2Part, _ = strconv.Atoi(v2Parts[i])
		}

		if v1Part > v2Part {
			return 1
		}
		if v1Part < v2Part {
			return -1
		}
	}
	return 0
}
