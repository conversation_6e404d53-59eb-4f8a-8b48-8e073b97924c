package services

import (
	"context"
	"henghengman-desktop/backend/constant"
	"henghengman-desktop/backend/pkg/converter"
	"henghengman-desktop/backend/pkg/downloader"
	"henghengman-desktop/backend/pkg/progress"
	"henghengman-desktop/backend/pkg/task"
	"henghengman-desktop/backend/pkg/utils"
	"henghengman-desktop/backend/storage"
	"henghengman-desktop/backend/types"
	"log/slog"
	"net/http"
	"os"
	"path"

	"github.com/wailsapp/wails/v2/pkg/runtime"
	"golang.org/x/sync/errgroup"
)

type Downloader struct {
	ctx              context.Context
	downloaderClient *downloader.Client
	convertClient    *converter.Client
	pref             *storage.PreferencesStorage
	taskManager      *task.Manager
}

func NewDownloader(pref *storage.PreferencesStorage, taskManager *task.Manager) *Downloader {
	return &Downloader{
		downloaderClient: downloader.NewClient(),
		convertClient:    converter.NewClient(),
		pref:             pref,
		taskManager:      taskManager,
	}
}

func (s *Downloader) Start(ctx context.Context) {
	s.ctx = ctx
}

func (s *Downloader) Submit(req types.DownloadReq) string {
	return task.NewStreamPromise(s.ctx, func(ctx context.Context, taskID string, resolve task.Resolve[types.DownloadProgress], reject task.Reject) error {
		return s.download(ctx, taskID, req, resolve)
	}).Then(func(taskID string, data types.DownloadProgress) {
		runtime.EventsEmit(s.ctx, constant.EventTaskDownloadProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Data:   data,
		})
	}).Catch(func(taskID string, err error) {
		runtime.EventsEmit(s.ctx, constant.EventTaskDownloadProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Error:  err.Error(),
		})
	}).Finally(func(ctx context.Context, taskID string) {
		runtime.EventsEmit(s.ctx, constant.EventTaskDownloadProgressKey, types.JSStreamResp{
			TaskID: taskID,
			Done:   true,
		})
	}).OnRegister(s.taskManager.Register).Start()
}

func (s *Downloader) Cancel(taskID string) {
	s.taskManager.Cancel(taskID)
}

func (s *Downloader) getDownloadTempFilePath(outputDir, downloadURL string) string {
	return path.Join(outputDir, utils.MD5([]byte(downloadURL))+constant.TempFileExt)
}

func (s *Downloader) download(ctx context.Context, taskID string, req types.DownloadReq, resolve task.Resolve[types.DownloadProgress]) error {
	wg, _ := errgroup.WithContext(ctx)
	totalProgress := progress.NewAggregator(0, func(total int64, done int64, speed int64, remainingTime int64) {
		resolve(taskID, types.DownloadProgress{
			Total:         total,
			Speed:         speed,
			RemainingTime: remainingTime,
		})
	})
	totalProgress.Start()
	defer totalProgress.Stop()
	for _, item := range req.FileURLs {
		downloadItem := item
		wg.Go(func() error {
			header := make(http.Header)
			for k, v := range downloadItem.Header {
				header.Set(k, v)
			}
			return s.downloaderClient.Download(ctx,
				downloadItem.URL,
				req.OutputDir,
				utils.MD5([]byte(downloadItem.URL))+constant.TempFileExt,
				downloader.WithProxy(utils.GetProxy(s.pref.GetPreferences().Network)),
				downloader.WithHeader(header),
				downloader.WithMonitor(totalProgress),
			)
		})
	}
	err := wg.Wait()
	if err != nil {
		return err
	}
	files := make([]string, 0, len(req.FileURLs))
	for _, item := range req.FileURLs {
		files = append(files, s.getDownloadTempFilePath(req.OutputDir, item.URL))
	}
	defer func() {
		for _, filePath := range files {
			err = os.Remove(filePath)
			if err != nil {
				slog.WarnContext(ctx, "删除临时文件", slog.Any("err", err))
			}
		}
	}()
	switch req.OutputType {
	case types.DownloadOutputTypeAudio:
		return s.convertClient.Audio(
			ctx,
			s.getDownloadTempFilePath(req.OutputDir, req.FileURLs[0].URL),
			req.OutputDir,
			req.OutputName,
		)
	case types.DownloadOutputTypeVideo:
		return s.convertClient.Video(
			ctx,
			files,
			req.OutputDir,
			req.OutputName,
		)
	case types.DownloadOutputTypeImage:
		return s.convertClient.Image(
			ctx,
			s.getDownloadTempFilePath(req.OutputDir, req.FileURLs[0].URL),
			req.OutputDir,
			req.OutputName,
		)
	default:
		err = os.Rename(s.getDownloadTempFilePath(req.OutputDir, req.FileURLs[0].URL), path.Join(req.OutputDir, req.OutputName))
		if err != nil {
			slog.WarnContext(ctx, "重命名文件", slog.Any("err", err))
		}
	}
	return nil
}
