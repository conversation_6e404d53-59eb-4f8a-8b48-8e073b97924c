package constant

// 应用相关常量
const (
	PackageName           = "hegnhengmao"
	UpgradeMembershipPath = "/upgrade"
	PersonalCenterPath    = "/profile"
	SharePath             = "/share"
	DefaultWindowWidth    = 1200
	DefaultWindowHeight   = 800
)

// 通用设置默认值
const (
	DefaultLanguage              = "zh-CN"
	DefaultDownloadDir           = "./downloads"
	DefaultAudioTrackLanguage    = "zh-CN"
	DefaultVideoResolution       = "1080p"
	DefaultAuthorNameCreateDir   = false
	DefaultPostClassifyCreateDir = false
	DefaultAddSerialNumber       = false
	DefaultSaveAutoAlterMD5      = false
)

// 网络设置默认值
const (
	DefaultNetworkEnable = false
	DefaultProxyRule     = "system"
)

// DefaultTestSites 默认测试站点
var DefaultTestSites = []struct {
	Name string
	URL  string
}{
	{"百度", "https://www.baidu.com"},
	{"谷歌", "https://www.google.com"},
}

// 媒体类型
const (
	MediaTypeVideo    = "video"
	MediaTypeAudio    = "audio"
	MediaTypeImage    = "image"
	MediaTypeSubtitle = "subtitle"
	MediaTypeOther    = "other"
)

// PlayListPageSize 播放列表每页数量，5的整数倍
const PlayListPageSize = 10 //

// ToolsPath 工具目录
const ToolsPath = "tools"

// TempFileExt 临时文件扩展名
const TempFileExt = ".tmp"

// DefaultUniqueId 默认应用id，用户单实例启动
const DefaultUniqueId = "ffe665c8-e741-42b7-9e7b-07904d198dbf"

// YtDlpDownloadURL 下载获取yt-dlp最新版本
const YtDlpDownloadURL = "https://api.snapany.com/desktop/download-ytdlp-latest-version"
