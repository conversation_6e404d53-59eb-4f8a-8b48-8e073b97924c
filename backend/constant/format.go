package constant

type MediaOutputFormat struct {
	Label string
	Value string
	Key   string
	Extra []string
	Ext   string
}

// VideoOutputFormats 视频输出格式
var VideoOutputFormats = []MediaOutputFormat{
	{Label: "MP4", Value: "mp4", Key: "mp4", Ext: ".mp4"},
	{Label: "MKV", Value: "mkv", Key: "mkv", Ext: ".mkv"},
	{Label: "MOV", Value: "mov", Key: "mov", Ext: ".mov"},
	{Label: "WEBM", Value: "webm", Key: "webm", Ext: ".webm"},
	{Label: "AVI", Value: "avi", Key: "avi", Ext: ".avi"},
	{Label: "FLV", Value: "flv", Key: "flv", Ext: ".flv"},
	{Label: "TS", Value: "ts", Key: "ts", Ext: ".ts"},
	{Label: "MPG", Value: "mpg", Key: "mpg", Ext: ".mpg"},
	{Label: "MPEG", Value: "mpeg", Key: "mpeg", Ext: ".mpeg"},
	{Label: "WMV", Value: "wmv", Key: "wmv", Ext: ".wmv"},
	{Label: "OGV", Value: "ogv", Key: "ogv", Ext: ".ogv"},
	{Label: "3GP", Value: "3gp", Key: "3gp", Ext: ".3gp"},
	{Label: "SWF", Value: "swf", Key: "swf", Ext: ".swf"},
	{Label: "VOB", Value: "vob", Key: "vob", Ext: ".vob"},
	{Label: "3G2", Value: "3g2", Key: "3g2", Extra: []string{"-s", "1408x1152", "-ar", "8000", "-ac", "1"}, Ext: ".3g2"},
	{Label: "GIF", Value: "gif", Key: "gif", Ext: ".gif"},
}

// AudioOutputFormats 音频输出格式
var AudioOutputFormats = []MediaOutputFormat{
	{Label: "MP3", Value: "mp3", Key: "mp3", Ext: ".mp3"},
	{Label: "AAC", Value: "m4a", Key: "aac", Ext: ".m4a"}, // AAC通常封装在M4A容器中
	{Label: "M4A", Value: "m4a", Key: "m4a", Ext: ".m4a"},
	{Label: "WAV", Value: "wav", Key: "wav", Ext: ".wav"},
	{Label: "FLAC", Value: "flac", Key: "flac", Ext: ".flac"},
	{Label: "OPUS", Value: "opus", Key: "opus", Ext: ".opus"},
	{Label: "OGG", Value: "ogg", Key: "ogg", Ext: ".ogg"},
	{Label: "OGA", Value: "oga", Key: "oga", Ext: ".oga"},
	{Label: "AIF", Value: "aif", Key: "aif", Ext: ".aif"},
	{Label: "AIFF", Value: "aiff", Key: "aiff", Ext: ".aiff"},
	{Label: "WMA", Value: "wma", Key: "wma", Ext: ".wma"},
	{Label: "ALAC", Value: "m4a", Key: "alac", Extra: []string{"-c:a", "alac"}, Ext: ".m4a"},
	{Label: "iPhone Ringtone", Value: "m4r", Key: "m4r", Extra: []string{"-f", "ipod"}, Ext: ".m4r"},
	{Label: "MP2", Value: "mp2", Key: "mp2", Ext: ".mp2"},
	{Label: "AMR", Value: "amr", Key: "amr", Extra: []string{"-ar", "8000", "-ac", "1"}, Ext: ".amr"},
	{Label: "MMF", Value: "mmf", Key: "mmf", Extra: []string{"-ar", "8000", "-ac", "1"}, Ext: ".mmf"}, // SMAF格式，主要用于早期日本手机铃声
}

// ImageOutputFormats 图片输出格式
var ImageOutputFormats = []MediaOutputFormat{
	{Label: "JPEG", Value: "jpeg", Key: "jpeg", Ext: ".jpeg"},
	{Label: "JPG", Value: "jpg", Key: "jpg", Ext: ".jpg"},
	{Label: "PNG", Value: "png", Key: "png", Ext: ".png"},
	{Label: "BMP", Value: "bmp", Key: "bmp", Ext: ".bmp"},
	{Label: "TIFF", Value: "tiff", Key: "tiff", Ext: ".tiff"},
	{Label: "WEBP", Value: "webp", Key: "webp", Ext: ".webp"},
	{Label: "GIF", Value: "gif", Key: "gif", Ext: ".gif"},
	{Label: "TGA", Value: "tga", Key: "tga", Ext: ".tga"},
	{Label: "AVIF", Value: "avif", Key: "avif", Ext: ".avif"},
	{Label: "EXR", Value: "exr", Key: "exr", Ext: ".exr"},
	{Label: "ICO", Value: "ico", Key: "ico", Extra: []string{"-vf", "scale='min(256,iw):min(256,ih)'"}, Ext: ".ico"},
}

var FormatToCodec = map[string][]string{
	"mp4":  {"h264", "hevc", "aac", "mp3", "alac"},
	"mkv":  {"h264", "hevc", "vp8", "vp9", "aac", "opus", "flac", "alac", "mp3", "ac3"},
	"mov":  {"h264", "hevc", "aac", "alac", "mp3"},
	"webm": {"vp8", "vp9", "opus", "vorbis"},
	"avi":  {"mpeg4", "h264", "mp3", "ac3"},
	"flv":  {"flv1", "h264", "aac", "mp3"},
	"ts":   {"h264", "hevc", "aac", "ac3", "mp2"},
	"mpg":  {"mpeg1video", "mpeg2video", "mp2", "mp3"},
	"mpeg": {"mpeg1video", "mpeg2video", "mp2", "mp3"},
	"wmv":  {"wmv1", "wmv2", "wmv3", "wma"},
	"ogv":  {"theora", "vorbis"},
	"3gp":  {"h263", "h264", "aac", "amr_nb", "amr_wb"},
	"swf":  {"flv1", "mp3", "adpcm_swf"},
	"vob":  {"mpeg2video", "ac3", "mp2", "mp3"},
	"3g2":  {"h263", "h264", "aac", "amr_nb"},
	"gif":  {"gif"},
	"mp3":  {"mp3"},
	"aac":  {"aac"},
	"m4a":  {"aac", "alac"},
	"wav":  {"pcm_s16le", "pcm_s24le"},
	"flac": {"flac"},
	"opus": {"opus"},
	"ogg":  {"vorbis", "opus", "flac"},
	"oga":  {"vorbis", "opus"},
	"aif":  {"pcm_s16be"},
	"aiff": {"pcm_s16be"},
	"wma":  {"wma"},
	"alac": {"alac"},
	"m4r":  {"aac", "alac"},
	"mp2":  {"mp2"},
	"amr":  {"amr_nb"},
	"mmf":  {"adpcm", "amr_nb"},
}

var FormatToEncoder = map[string]string{
	// 视频格式
	"mp4":  "libx264",
	"mkv":  "libx264",
	"mov":  "libx264",
	"webm": "libvpx",
	"avi":  "mpeg4",
	"flv":  "flv",
	"ts":   "libx264",
	"mpg":  "mpeg2video",
	"mpeg": "mpeg2video",
	"wmv":  "wmv2",
	"ogv":  "libtheora",
	"3gp":  "h263",
	"swf":  "flv",
	"vob":  "mpeg2video",
	"3g2":  "h263",
	"gif":  "gif",
	// 音频格式
	"mp3":  "libmp3lame",
	"aac":  "aac",
	"m4a":  "aac",
	"wav":  "pcm_s16le",
	"flac": "flac",
	"opus": "libopus",
	"ogg":  "libvorbis",
	"oga":  "libvorbis",
	"aif":  "pcm_s16be",
	"aiff": "pcm_s16be",
	"wma":  "wmav2",
	"alac": "alac",
	"m4r":  "aac",
	"mp2":  "mp2",
	"amr":  "libopencore_amrnb",
	"mmf":  "adpcm_ms",
}
